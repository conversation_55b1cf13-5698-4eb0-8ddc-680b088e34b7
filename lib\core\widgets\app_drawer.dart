import 'dart:io';
import 'package:flutter/material.dart';
import '../routes/app_routes.dart';
import '../theme/index.dart';
import '../auth/services/auth_service.dart';
import '../../features/users/models/user.dart';
import '../../features/accounts/screens/accounts_screen.dart';
import '../../features/accounts/screens/chart_of_accounts_screen.dart';
import '../../features/accounts/screens/financial_reports_screen.dart';
import '../../features/accounts/screens/account_opening_balance_screen.dart';
import '../../features/accounts/screens/simple_account_form_screen.dart';
import '../../features/accounts/screens/journal_entry_form_screen.dart';
// تم حذف simple_journal_entry_form_screen.dart - مكررة
import '../../features/categories/screens/category_form_screen.dart';
import '../../features/warehouses/screens/warehouse_form_screen.dart';
import '../../features/vouchers/screens/receipt_voucher_screen.dart';
import '../../features/vouchers/screens/payment_voucher_screen.dart';

import '../../features/categories/screens/categories_screen.dart';
import '../../features/customers/screens/customers_screen.dart';
import '../../features/error_logs/screens/error_logs_screen.dart';
import '../../features/products/screens/products_screen.dart';
import '../../features/products/screens/product_opening_balance_screen.dart';
import '../../features/promotions/screens/promotions_screen.dart';
import '../../features/purchases/screens/purchases_screen.dart';
import '../../features/sales/screens/sales_screen.dart';

import '../../features/settings/screens/settings_screen.dart';
import '../../features/suppliers/screens/suppliers_screen.dart';
import '../../features/units/screens/units_screen.dart';
import '../../features/warehouses/screens/warehouse_home_screen.dart';
import '../../features/warehouses/screens/warehouse_management_screen.dart';
import '../../features/warehouses/screens/inventory_management_screen.dart';
import '../../features/vouchers/screens/vouchers_screen.dart';
import '../../features/vouchers/screens/journal_voucher_screen.dart';
import '../../features/vouchers/screens/double_entry_voucher_screen.dart';
import '../../features/currencies/screens/currencies_screen.dart';
import '../../features/currencies/screens/currency_exchange_screen.dart';
import '../../features/currencies/screens/currency_management_screen.dart';
import '../../features/currencies/screens/currency_buy_sell_screen.dart';
import '../../features/currencies/screens/currency_reports_screen.dart';
import '../../features/settings/screens/printer_settings_screen.dart';
import '../../features/users/screens/users_screen.dart';
import '../../features/users/screens/user_form_screen.dart'; // إضافة استيراد شاشة تعديل المستخدم
import '../../features/payrolls/screens/payrolls_screen.dart';
import '../../features/payrolls/screens/payroll_form_screen_simple.dart';
import '../../features/employees/screens/unified_employees_screen.dart';
import '../../features/employees/screens/unified_leave_screen.dart';
import '../../features/employees/screens/unified_employee_reports_screen.dart';

/// قائمة جانبية موحدة للتطبيق
class AppDrawer extends StatefulWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  User? _currentUser;
  String? _currentBranchId;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إعادة تحميل بيانات المستخدم عند تغيير التبعيات
    _loadCurrentUser();
  }

  /// تحميل بيانات المستخدم الحالي
  Future<void> _loadCurrentUser() async {
    try {
      final user = await _authService.getCurrentUser();
      final branchId = await _authService.getCurrentBranchId();

      if (mounted) {
        setState(() {
          _currentUser = user;
          _currentBranchId = branchId;
        });
      }
    } catch (e) {
      // في حالة الخطأ، سيتم عرض رأس افتراضي
    }
  }

  /// عرض حوار تفاصيل المستخدم الكاملة
  void _showUserDetailsDialog() {
    // إذا لم تكن بيانات المستخدم محملة، حاول تحميلها أولاً
    if (_currentUser == null) {
      _loadCurrentUser().then((_) {
        if (mounted && _currentUser != null) {
          _showUserDetailsDialog();
        } else if (mounted) {
          // عرض رسالة خطأ للمستخدم
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن تحميل بيانات المستخدم'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      });
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.85,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  DynamicColors.surface(context),
                  DynamicColors.surface(context).withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // المحتوى القابل للتمرير
                Flexible(
                  child: SingleChildScrollView(
                    padding: AppDimensions.cardPaddingLarge,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // رأس الحوار مع صورة المستخدم
                        Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: DynamicColors.primary,
                                  width: 2,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: _currentUser!.avatar != null &&
                                        _currentUser!.avatar!.isNotEmpty
                                    ? _buildUserAvatar(_currentUser!.avatar!)
                                    : _buildDefaultAvatar(),
                              ),
                            ),
                            const SizedBox(width: AppDimensions.spacing16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // إصلاح عرض النص الكبير مع ضمان ظهوره كاملاً
                                  Text(
                                    'بيانات المستخدم',
                                    style: AppTypography(
                                      fontSize: 16, // تقليل حجم الخط قليلاً
                                      fontWeight: FontWeight.bold,
                                      color: DynamicColors.textPrimary(context),
                                    ),
                                    maxLines: 2, // السماح بسطرين كحد أقصى
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),
                                  Text(
                                    _currentUser!.fullName,
                                    style: AppTypography(
                                      fontSize: 14,
                                      color:
                                          DynamicColors.textSecondary(context),
                                    ),
                                    maxLines: 2, // السماح بسطرين للاسم الطويل
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: Icon(
                                Icons.close,
                                color: DynamicColors.textSecondary(context),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppDimensions.spacing24),
                        // تفاصيل المستخدم
                        _buildUserDetailItem(
                          'الاسم الكامل',
                          _currentUser!.fullName,
                          Icons.person,
                        ),
                        _buildUserDetailItem(
                          'اسم المستخدم',
                          _currentUser!.username,
                          Icons.account_circle,
                        ),
                        if (_currentUser!.email != null)
                          _buildUserDetailItem(
                            'البريد الإلكتروني',
                            _currentUser!.email!,
                            Icons.email,
                          ),
                        if (_currentUser!.phone != null)
                          _buildUserDetailItem(
                            'رقم الهاتف',
                            _currentUser!.phone!,
                            Icons.phone,
                          ),
                        _buildUserDetailItem(
                          'الدور',
                          _currentUser!.roleName ?? 'غير محدد',
                          Icons.admin_panel_settings,
                        ),
                        if (_currentUser!.branchName != null)
                          _buildUserDetailItem(
                            'الفرع',
                            _currentUser!.branchName!,
                            Icons.store_mall_directory,
                          ),
                        _buildUserDetailItem(
                          'حالة الحساب',
                          _currentUser!.isActive ? 'نشط' : 'غير نشط',
                          _currentUser!.isActive
                              ? Icons.check_circle
                              : Icons.cancel,
                          valueColor: _currentUser!.isActive
                              ? AppColors.success
                              : AppColors.error,
                        ),
                        if (_currentUser!.lastLogin != null)
                          _buildUserDetailItem(
                            'آخر تسجيل دخول',
                            _formatDateTime(_currentUser!.lastLogin!),
                            Icons.access_time,
                          ),
                        const SizedBox(height: AppDimensions.spacing16),
                      ],
                    ),
                  ),
                ),
                // أزرار الإجراءات الثابتة في الأسفل
                Container(
                  padding: AppDimensions.cardPaddingLarge,
                  decoration: BoxDecoration(
                    color: DynamicColors.surface(context),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                    border: Border(
                      top: BorderSide(
                        color: DynamicColors.border(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      // زر تعديل البيانات - أصغر وأكثر أناقة
                      Expanded(
                        child: Container(
                          height: 44, // ارتفاع أصغر
                          child: ElevatedButton(
                            onPressed: () {
                              // إغلاق حوار تفاصيل المستخدم
                              Navigator.of(context).pop();
                              // الانتقال إلى شاشة تعديل المستخدم مع تمرير بيانات المستخدم الحالي
                              _navigateToUserEditForm();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DynamicColors.primary,
                              foregroundColor: AppColors.onPrimary,
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.edit, size: 18),
                                SizedBox(width: AppDimensions.spacing8),
                                Text(
                                  'تعديل',
                                  style: AppTypography(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.onPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppDimensions.spacing12),
                      // زر تسجيل الخروج - أصغر وأكثر أناقة
                      Expanded(
                        child: Container(
                          height: 44, // ارتفاع أصغر
                          child: OutlinedButton(
                            onPressed: () async {
                              Navigator.of(context).pop();
                              final success = await _authService.logout();
                              if (success && context.mounted) {
                                Navigator.pushNamedAndRemoveUntil(
                                  context,
                                  '/login',
                                  (route) => false,
                                );
                              }
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppColors.error,
                              side: const BorderSide(
                                  color: AppColors.error, width: 1.5),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.logout, size: 18),
                                SizedBox(width: AppDimensions.spacing8),
                                Text(
                                  'خروج',
                                  style: AppTypography(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.error,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء رأس القائمة الجانبية المحسن مع بيانات المستخدم
  Widget _buildEnhancedDrawerHeader() {
    return Container(
      height: 140, // تقليل الارتفاع بعد حذف قسم التطبيق
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            // استخدام اللون الأساسي الديناميكي المختار من قبل المستخدم
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
            Theme.of(context).primaryColor.withValues(alpha: 0.6),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        // إضافة ظل خفيف للعمق يتكيف مع اللون الأساسي
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: AppDimensions.cardPaddingLarge,
          child: Row(
            children: [
              // صورة المستخدم قابلة للنقر لعرض التفاصيل
              GestureDetector(
                onTap: _showUserDetailsDialog,
                child: Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.onPrimary.withValues(alpha: 0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            AppColors.lightTextPrimary.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(32),
                    child: _currentUser?.avatar != null &&
                            _currentUser!.avatar!.isNotEmpty
                        ? _buildUserAvatar(_currentUser!.avatar!)
                        : _buildDefaultAvatar(),
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.spacing16),
              // معلومات المستخدم الأساسية
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _currentUser?.fullName ?? 'مستخدم النظام',
                      style: const AppTypography(
                        color: AppColors.onPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.spacing4),
                    Text(
                      _currentUser?.roleName ?? 'مستخدم',
                      style: AppTypography(
                        color: AppColors.onPrimary.withValues(alpha: 0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (_currentUser?.branchName != null) ...[
                      const SizedBox(height: AppDimensions.spacing2),
                      Row(
                        children: [
                          Icon(
                            Icons.store_mall_directory,
                            size: 14,
                            color: AppColors.onPrimary.withValues(alpha: 0.8),
                          ),
                          const SizedBox(width: AppDimensions.spacing4),
                          Expanded(
                            child: Text(
                              _currentUser!.branchName!,
                              style: AppTypography(
                                color:
                                    AppColors.onPrimary.withValues(alpha: 0.8),
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              // أيقونة لإظهار أن البروفايل قابل للنقر
              Icon(
                Icons.info_outline,
                color: AppColors.onPrimary.withValues(alpha: 0.7),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء صورة المستخدم من الملف المحلي أو الرابط
  Widget _buildUserAvatar(String avatarPath) {
    // التحقق من نوع المسار (محلي أم رابط)
    if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
      // صورة من الإنترنت
      return Image.network(
        avatarPath,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else {
      // صورة محلية
      return Image.file(
        File(avatarPath),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    }
  }

  /// بناء صورة المستخدم الافتراضية
  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.onPrimary.withValues(alpha: 0.2),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: AppColors.onPrimary,
        size: 32,
      ),
    );
  }

  /// بناء عنصر تفاصيل المستخدم
  Widget _buildUserDetailItem(
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.spacing8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: DynamicColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: DynamicColors.primary,
            ),
          ),
          const SizedBox(width: AppDimensions.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTypography(
                    fontSize: 12,
                    color: DynamicColors.textSecondary(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing2),
                Text(
                  value,
                  style: AppTypography(
                    fontSize: 14,
                    color: valueColor ?? DynamicColors.textPrimary(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضى';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }

  /// الانتقال إلى شاشة تعديل المستخدم الحالي
  void _navigateToUserEditForm() {
    if (_currentUser == null) return;

    // استيراد شاشة تعديل المستخدم
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: _currentUser),
      ),
    ).then((result) {
      // إعادة تحميل بيانات المستخدم بعد العودة من شاشة التعديل
      if (mounted) {
        // إعادة تحميل بيانات المستخدم من قاعدة البيانات
        _loadCurrentUser().then((_) {
          // إعادة بناء الواجهة لتحديث رأس القائمة الجانبية
          if (mounted) {
            setState(() {
              // تحديث الحالة لإعادة بناء الواجهة
            });
          }
        });
      }
    });
  }

  // دالة للانتقال إلى شاشة معينة
  void _navigateTo(BuildContext context, Widget screen) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  // دالة لبناء عنصر فرعي في القائمة
  Widget _buildSubMenuItem({
    required BuildContext context,
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    // تعيين لون افتراضي للأيقونة إذا لم يتم تحديد لون
    final color = iconColor ?? _getIconColor(icon);

    return ListTile(
      contentPadding: const EdgeInsets.only(right: 32.0),
      leading: Icon(
        icon,
        size: 20,
        color: color,
      ),
      title: Text(title),
      onTap: onTap,
    );
  }

  // دالة لبناء قسم قابل للتوسيع في القائمة
  Widget _buildExpandableSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? iconColor,
  }) {
    // تعيين لون افتراضي للأيقونة إذا لم يتم تحديد لون
    final color = iconColor ?? _getIconColor(icon);

    return ExpansionTile(
      leading: Icon(
        icon,
        color: color,
      ),
      title: Text(title),
      children: children,
    );
  }

  // دالة للحصول على لون مناسب للأيقونة بناءً على نوعها - محسنة للنظام الموحد
  Color _getIconColor(IconData icon) {
    if (icon == Icons.shopping_cart || icon == Icons.shopping_cart_checkout) {
      return AppColors.success;
    } else if (icon == Icons.store ||
        icon == Icons.inventory ||
        icon == Icons.inventory_2) {
      return AppColors.info;
    } else if (icon == Icons.warehouse || icon == Icons.warehouse_outlined) {
      return AppColors.secondary;
    } else if (icon == Icons.people || icon == Icons.person) {
      return AppColors.accent;
    } else if (icon == Icons.monetization_on ||
        icon == Icons.currency_exchange) {
      return AppColors.warning;
    } else if (icon == Icons.receipt_long || icon == Icons.receipt) {
      return AppColors.warning;
    } else if (icon == Icons.account_balance ||
        icon == Icons.account_balance_wallet) {
      return DynamicColors.primary;
    } else if (icon == Icons.campaign || icon == Icons.local_offer) {
      return AppColors.error;
    } else if (icon == Icons.settings || icon == Icons.settings_applications) {
      return AppColors.lightTextSecondary;
    } else if (icon == Icons.history || icon == Icons.history_toggle_off) {
      return AppColors.secondary;
    } else if (icon == Icons.error_outline) {
      return AppColors.error;
    } else if (icon == Icons.add_box || icon == Icons.add_chart) {
      return AppColors.success;
    } else if (icon == Icons.category) {
      return AppColors.warning;
    } else if (icon == Icons.straighten) {
      return AppColors.lightTextSecondary;
    } else if (icon == Icons.arrow_downward) {
      return AppColors.success;
    } else if (icon == Icons.arrow_upward) {
      return AppColors.error;
    } else if (icon == Icons.compare_arrows || icon == Icons.swap_horiz) {
      return AppColors.accent;
    } else if (icon == Icons.edit_note) {
      return AppColors.warning;
    } else if (icon == Icons.local_shipping) {
      return AppColors.info;
    } else if (icon == Icons.home) {
      return DynamicColors.primary;
    } else if (icon == Icons.account_tree) {
      return AppColors.secondary;
    } else if (icon == Icons.bar_chart) {
      return DynamicColors.primary;
    } else if (icon == Icons.cloud_sync) {
      return AppColors.info;
    } else if (icon == Icons.storage) {
      return AppColors.lightTextSecondary;
    } else if (icon == Icons.print) {
      return AppColors.warning;
    } else if (icon == Icons.people_alt) {
      return AppColors.accent;
    }

    // لون افتراضي إذا لم يتم العثور على لون مناسب
    return DynamicColors.primary;
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // رأس القائمة المحسن مع بيانات المستخدم الحالي
          _buildEnhancedDrawerHeader(),

          // قسم المبيعات والمشتريات
          _buildExpandableSection(
            context: context,
            title: 'المبيعات والمشتريات',
            icon: Icons.shopping_cart,
            children: [
              // قسم المبيعات
              _buildExpandableSection(
                context: context,
                title: 'المبيعات',
                icon: Icons.shopping_cart_checkout,
                children: [
                  _buildSubMenuItem(
                    context: context,
                    title: 'فواتير المبيعات',
                    icon: Icons.receipt,
                    onTap: () => _navigateTo(context, const SalesScreen()),
                  ),
                  _buildSubMenuItem(
                    context: context,
                    title: 'مردود المبيعات',
                    icon: Icons.assignment_return,
                    onTap: () => _navigateTo(context,
                        const SalesScreen()), // يجب تغييرها لشاشة مردود المبيعات
                  ),
                ],
              ),

              // قسم المشتريات
              _buildExpandableSection(
                context: context,
                title: 'المشتريات',
                icon: Icons.store,
                children: [
                  _buildSubMenuItem(
                    context: context,
                    title: 'فواتير المشتريات',
                    icon: Icons.receipt,
                    onTap: () => _navigateTo(context, const PurchasesScreen()),
                  ),
                  _buildSubMenuItem(
                    context: context,
                    title: 'مردود المشتريات',
                    icon: Icons.assignment_return,
                    onTap: () => _navigateTo(context,
                        const PurchasesScreen()), // يجب تغييرها لشاشة مردود المشتريات
                  ),
                ],
              ),

              // تقارير الفواتير
              _buildSubMenuItem(
                context: context,
                title: 'تقارير الفواتير',
                icon: Icons.bar_chart,
                onTap: () {}, // يجب إضافة شاشة تقارير الفواتير
              ),
            ],
          ),

          // قسم المخزون
          _buildExpandableSection(
            context: context,
            title: 'الأصناف والمنتجات',
            icon: Icons.inventory,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'المنتجات',
                icon: Icons.inventory_2,
                onTap: () => _navigateTo(context, const ProductsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة منتج جديد',
                icon: Icons.add_box,
                onTap: () =>
                    Navigator.of(context).pushNamed(AppRoutes.productForm),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'رصيد أول المدة',
                icon: Icons.add_chart,
                onTap: () =>
                    _navigateTo(context, const ProductOpeningBalanceScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'التصنيفات',
                icon: Icons.category,
                onTap: () => _navigateTo(context, const CategoriesScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'الوحدات',
                icon: Icons.straighten,
                onTap: () => _navigateTo(context, const UnitsScreen()),
              ),
            ],
          ),

          // قسم المخازن
          _buildExpandableSection(
            context: context,
            title: 'المخازن',
            icon: Icons.warehouse,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'الرئيسية',
                icon: Icons.home,
                onTap: () => _navigateTo(context, const WarehouseHomeScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إدارة المخازن',
                icon: Icons.warehouse_outlined,
                onTap: () =>
                    _navigateTo(context, const WarehouseManagementScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إدارة المخزون',
                icon: Icons.inventory_2,
                onTap: () =>
                    _navigateTo(context, const InventoryManagementScreen()),
              ),
            ],
          ),

          // قسم العملاء والموردين
          _buildExpandableSection(
            context: context,
            title: 'العملاء والموردين',
            icon: Icons.people,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'العملاء',
                icon: Icons.person,
                onTap: () => _navigateTo(context, const CustomersScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'الموردين',
                icon: Icons.local_shipping,
                onTap: () => _navigateTo(context, const SuppliersScreen()),
              ),
            ],
          ),

          // قسم العملات
          _buildExpandableSection(
            context: context,
            title: 'العملات',
            icon: Icons.monetization_on,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'الرئيسية',
                icon: Icons.home,
                onTap: () => _navigateTo(context, const CurrenciesScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'شراء العملات',
                icon: Icons.arrow_downward,
                onTap: () => _navigateTo(
                    context, const CurrencyBuySellScreen(isBuy: true)),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'بيع العملات',
                icon: Icons.arrow_upward,
                onTap: () => _navigateTo(
                    context, const CurrencyBuySellScreen(isBuy: false)),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'مصارفة العملات',
                icon: Icons.currency_exchange,
                onTap: () =>
                    _navigateTo(context, const CurrencyExchangeScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إدارة العملات',
                icon: Icons.settings,
                onTap: () =>
                    _navigateTo(context, const CurrencyManagementScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'تقارير العملات',
                icon: Icons.bar_chart,
                onTap: () =>
                    _navigateTo(context, const CurrencyReportsScreen()),
              ),
            ],
          ),

          // قسم السندات
          _buildExpandableSection(
            context: context,
            title: 'السندات',
            icon: Icons.receipt_long,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'الرئيسية',
                icon: Icons.home,
                onTap: () => _navigateTo(context, const VouchersScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'سند قبض',
                icon: Icons.arrow_downward,
                onTap: () => _navigateTo(context, const ReceiptVoucherScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'سند صرف',
                icon: Icons.arrow_upward,
                onTap: () => _navigateTo(context, const PaymentVoucherScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'قيد بسيط',
                icon: Icons.compare_arrows,
                onTap: () => _navigateTo(context, const JournalVoucherScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'قيد مزدوج',
                icon: Icons.swap_horiz,
                onTap: () =>
                    _navigateTo(context, const DoubleEntryVoucherScreen()),
              ),
            ],
          ),

          // قسم الحسابات
          _buildExpandableSection(
            context: context,
            title: 'الحسابات',
            icon: Icons.account_balance,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'الحسابات',
                icon: Icons.account_balance_wallet,
                onTap: () => _navigateTo(context, const AccountsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة حساب جديد',
                icon: Icons.add_circle_outline,
                onTap: () =>
                    _navigateTo(context, const SimpleAccountFormScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'رصيد أول المدة',
                icon: Icons.add_chart,
                onTap: () =>
                    _navigateTo(context, const AccountOpeningBalanceScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'شجرة الحسابات',
                icon: Icons.account_tree,
                onTap: () =>
                    _navigateTo(context, const ChartOfAccountsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'شجرة الحسابات المحسنة',
                icon: Icons.account_tree,
                iconColor: AppColors.accent,
                onTap: () => Navigator.of(context)
                    .pushNamed(AppRoutes.enhancedChartOfAccounts),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'التقارير المالية',
                icon: Icons.bar_chart,
                onTap: () =>
                    _navigateTo(context, const FinancialReportsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'ترحيل القيود المحاسبية',
                icon: Icons.publish,
                iconColor: AppColors.warning,
                onTap: () => Navigator.of(context)
                    .pushNamed(AppRoutes.journalEntryPosting),
              ),
            ],
          ),

          // قسم المصروفات
          _buildExpandableSection(
            context: context,
            title: 'المصروفات',
            icon: Icons.money_off,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'المصروفات',
                icon: Icons.payments,
                onTap: () => Navigator.pushNamed(context, AppRoutes.expenses),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'فئات المصروفات',
                icon: Icons.category,
                onTap: () =>
                    Navigator.pushNamed(context, AppRoutes.expenseCategories),
              ),
            ],
          ),

          // قسم التسويق
          _buildExpandableSection(
            context: context,
            title: 'التسويق',
            icon: Icons.campaign,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'العروض والإعلانات',
                icon: Icons.local_offer,
                onTap: () => _navigateTo(context, const PromotionsScreen()),
              ),
            ],
          ),

          // قسم الموارد البشرية
          _buildExpandableSection(
            context: context,
            title: 'الموارد البشرية',
            icon: Icons.people_alt,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'الموظفين',
                icon: Icons.person,
                onTap: () =>
                    _navigateTo(context, const UnifiedEmployeesScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'الإجازات',
                icon: Icons.event_note,
                onTap: () => _navigateTo(context, const UnifiedLeaveScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'الرواتب',
                icon: Icons.payments,
                onTap: () => _navigateTo(context, const PayrollsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة راتب جديد',
                icon: Icons.add_circle_outline,
                onTap: () =>
                    _navigateTo(context, const PayrollFormScreenSimple()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'تقارير الموظفين',
                icon: Icons.bar_chart,
                onTap: () =>
                    _navigateTo(context, const UnifiedEmployeeReportsScreen()),
              ),
            ],
          ),

          // قسم المستخدمين والفروع
          _buildExpandableSection(
            context: context,
            title: 'المستخدمين والفروع',
            icon: Icons.admin_panel_settings,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'إدارة المستخدمين',
                icon: Icons.person,
                onTap: () => _navigateTo(context, const UsersScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إدارة الفروع',
                icon: Icons.store_mall_directory,
                onTap: () => Navigator.pushNamed(context, AppRoutes.branches),
              ),
            ],
          ),

          // قسم الإعدادات
          _buildExpandableSection(
            context: context,
            title: 'الإعدادات',
            icon: Icons.settings,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'إعدادات التطبيق',
                icon: Icons.settings_applications,
                onTap: () => _navigateTo(context, const SettingsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'بيانات الملف الشخصي',
                icon: Icons.business,
                onTap: () =>
                    Navigator.pushNamed(context, AppRoutes.companyProfile),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إعدادات الطابعة',
                icon: Icons.print,
                onTap: () =>
                    _navigateTo(context, const PrinterSettingsScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'الشروط والسياسات',
                icon: Icons.policy,
                onTap: () => Navigator.pushNamed(context, AppRoutes.legal),
              ),
            ],
          ),

          // قسم الشاشات الجديدة
          _buildExpandableSection(
            context: context,
            title: 'الشاشات الجديدة',
            icon: Icons.new_releases,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'إضافة حساب جديد',
                icon: Icons.account_balance_wallet,
                onTap: () =>
                    _navigateTo(context, const SimpleAccountFormScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة راتب جديد',
                icon: Icons.payments,
                onTap: () =>
                    _navigateTo(context, const PayrollFormScreenSimple()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة قيد محاسبي',
                icon: Icons.receipt_long,
                onTap: () =>
                    _navigateTo(context, const JournalEntryFormScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة تصنيف',
                icon: Icons.category,
                onTap: () => _navigateTo(context, const CategoryFormScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'إضافة مخزن',
                icon: Icons.store,
                onTap: () => _navigateTo(context, const WarehouseFormScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'سند قبض',
                icon: Icons.arrow_downward,
                onTap: () => _navigateTo(context, const ReceiptVoucherScreen()),
              ),
              _buildSubMenuItem(
                context: context,
                title: 'سند صرف',
                icon: Icons.arrow_upward,
                onTap: () => _navigateTo(context, const PaymentVoucherScreen()),
              ),
            ],
          ),

          // قسم السجلات
          _buildExpandableSection(
            context: context,
            title: 'السجلات',
            icon: Icons.history,
            children: [
              _buildSubMenuItem(
                context: context,
                title: 'سجلات النشاط',
                icon: Icons.history_toggle_off,
                onTap: () {
                  Navigator.pop(context);
                  AppRoutes.navigateTo(context, AppRoutes.activityLog);
                },
              ),
              _buildSubMenuItem(
                context: context,
                title: 'سجلات الأخطاء',
                icon: Icons.error_outline,
                onTap: () => _navigateTo(context, const ErrorLogsScreen()),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
