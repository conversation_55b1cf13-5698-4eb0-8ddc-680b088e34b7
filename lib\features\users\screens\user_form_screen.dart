import 'dart:io';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';
import '../../../core/services/image_service.dart';
import '../../../core/services/image_storage_service.dart';

import '../../../core/widgets/index.dart';
import '../models/user.dart';

import '../presenters/user_presenter.dart';
import '../../../core/theme/index.dart';
import '../../../core/auth/enums/branch_access_type.dart';

/// شاشة نموذج المستخدم (إضافة/تعديل)
class UserFormScreen extends StatefulWidget {
  final User? user;

  const UserFormScreen({Key? key, this.user}) : super(key: key);

  @override
  State<UserFormScreen> createState() => _UserFormScreenState();
}

class _UserFormScreenState extends State<UserFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  String? _selectedRole;
  String? _selectedGroupId;
  String? _selectedBranchId;
  String? _selectedBranchAccessType = 'single_branch'; // نوع الوصول للفروع
  bool _isActive = true;
  bool _isLoading = false;
  bool _isNewUser = true;

  // متغيرات الصورة
  File? _selectedImage;
  String? _currentImageUrl;
  final ImageService _imageService = ImageService();
  final ImageStorageService _imageStorageService = ImageStorageService();

  // سيتم تحميل الأدوار من قاعدة البيانات
  List<Map<String, dynamic>> _roles = [];

  // استخدام التحميل الكسول
  late final UserPresenter _userPresenter;

  @override
  void initState() {
    super.initState();
    _isNewUser = widget.user == null;
    _userPresenter =
        AppProviders.getLazyPresenter<UserPresenter>(() => UserPresenter());

    // تحميل البيانات عند بدء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _userPresenter.loadGroups();
      _userPresenter.loadRoles(); // تحميل الأدوار من قاعدة البيانات
      _userPresenter.loadBranches(); // تحميل الفروع من قاعدة البيانات
      _loadRoles(); // تحويل الأدوار إلى التنسيق المطلوب
      _initUserData();
    });
  }

  /// تحميل الأدوار من قاعدة البيانات وتحويلها إلى التنسيق المطلوب
  Future<void> _loadRoles() async {
    try {
      // تحويل الأدوار من قاعدة البيانات إلى التنسيق المطلوب
      List<Map<String, String>> roles = _userPresenter.roles.map((role) {
        return {
          'value': role.id, // استخدام المعرف (ID) بدلاً من الرمز (name)
          'label':
              role.displayName, // استخدام الاسم المعروض للعرض في واجهة المستخدم
        };
      }).toList();

      // إذا كانت القائمة فارغة، نحاول إعادة تحميل الأدوار
      if (roles.isEmpty) {
        AppLogger.warning('قائمة الأدوار فارغة، سيتم إعادة تحميلها');
        // إعادة تحميل الأدوار من قاعدة البيانات
        await _userPresenter.loadRoles();
        if (_userPresenter.roles.isNotEmpty) {
          roles = _userPresenter.roles.map((role) {
            return {
              'value': role.id,
              'label': role.displayName,
            };
          }).toList();
        }
      }

      setState(() {
        _roles = roles;
        // تعيين القيمة الافتراضية للدور إذا لم تكن محددة
        if (_roles.isNotEmpty &&
            (_selectedRole == null || _selectedRole?.isEmpty == true)) {
          _selectedRole = _roles.first['value'];
        }
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل الأدوار: $e');
      // في حالة الخطأ، نترك القائمة فارغة وسيتم عرض رسالة مناسبة في واجهة المستخدم
      setState(() {
        _roles = [];
      });
    }
  }

  /// تهيئة بيانات المستخدم إذا كان في وضع التعديل
  void _initUserData() {
    if (widget.user != null) {
      try {
        _usernameController.text = widget.user!.username;
        _fullNameController.text = widget.user!.fullName;
        _emailController.text = widget.user!.email ?? '';
        _phoneController.text = widget.user!.phone ?? '';
        _selectedRole = widget.user!.roleId; // استخدام معرف الدور مباشرة
        _selectedBranchId = widget.user!.branchId; // استخدام معرف الفرع مباشرة
        _selectedBranchAccessType = widget.user!.branchAccessType ??
            'single_branch'; // نوع الوصول للفروع
        _isActive = widget.user!.isActive;
        _currentImageUrl = widget.user!.avatar; // تحميل صورة المستخدم الحالية

        // تأجيل تعيين مجموعة المستخدم حتى يتم تحميل المجموعات
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_userPresenter.groups.isNotEmpty) {
            // التحقق من وجود المجموعة المحددة في قائمة المجموعات
            final userGroupId = widget.user!.userGroupId;
            if (userGroupId != null) {
              final groupExists =
                  _userPresenter.groups.any((group) => group.id == userGroupId);
              if (groupExists) {
                setState(() {
                  _selectedGroupId = userGroupId;
                });
              } else {
                // إذا كانت المجموعة غير موجودة، نعين أول مجموعة في القائمة
                if (_userPresenter.groups.isNotEmpty) {
                  setState(() {
                    _selectedGroupId = _userPresenter.groups.first.id;
                  });
                }
              }
            }
          }
        });
      } catch (e) {
        // استخدام AppLogger بدلاً من print
        AppLogger.error('خطأ في تهيئة بيانات المستخدم: $e');
        // في حالة حدوث خطأ، نعين قيم افتراضية
        _usernameController.text = '';
        _fullNameController.text = '';
        _emailController.text = '';
        _phoneController.text = '';
        _selectedRole = null; // سيتم تعيين القيمة الافتراضية لاحقاً
        _isActive = true;
      }
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: _isNewUser ? 'إضافة مستخدم جديد' : 'تعديل المستخدم',
        showBackButton: true,
      ),
      body: ListenableBuilder(
        listenable: _userPresenter,
        builder: (context, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AkCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'معلومات المستخدم',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // الاسم الكامل
                          AkTextInput(
                            controller: _fullNameController,
                            label: 'الاسم الكامل',
                            hint: 'أدخل الاسم الكامل',
                            isRequired: true,
                            prefixIcon: Icons.badge,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الاسم الكامل';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // اسم المستخدم
                          AkTextInput(
                            controller: _usernameController,
                            label: 'اسم المستخدم',
                            hint: 'أدخل اسم المستخدم',
                            isRequired: true,
                            prefixIcon: Icons.person,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال اسم المستخدم';
                              }
                              if (value.length < 3) {
                                return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // كلمة المرور
                          AkPasswordInput(
                            controller: _passwordController,
                            label: _isNewUser
                                ? 'كلمة المرور'
                                : 'كلمة المرور الجديدة (اختياري)',
                            hint: _isNewUser
                                ? 'أدخل كلمة المرور'
                                : 'اتركه فارغاً للاحتفاظ بكلمة المرور الحالية',
                            isRequired: _isNewUser,
                            showStrengthIndicator:
                                true, // إضافة مؤشر قوة كلمة المرور
                            validator: (value) {
                              if (_isNewUser) {
                                // للمستخدم الجديد: كلمة المرور مطلوبة
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال كلمة المرور';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                              } else {
                                // للمستخدم الموجود: كلمة المرور اختيارية
                                if (value != null &&
                                    value.isNotEmpty &&
                                    value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // تأكيد كلمة المرور (يظهر فقط إذا تم إدخال كلمة مرور)
                          if (_isNewUser ||
                              _passwordController.text.isNotEmpty) ...[
                            AkPasswordInput(
                              controller: _confirmPasswordController,
                              label: 'تأكيد كلمة المرور',
                              hint: 'أعد إدخال كلمة المرور',
                              isRequired: _isNewUser,
                              showStrengthIndicator:
                                  false, // لا نحتاج مؤشر في حقل التأكيد
                              validator: (value) {
                                if (_isNewUser ||
                                    _passwordController.text.isNotEmpty) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى تأكيد كلمة المرور';
                                  }
                                  if (value != _passwordController.text) {
                                    return 'كلمة المرور غير متطابقة';
                                  }
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacing16),
                          ],

                          // البريد الإلكتروني
                          AkTextInput(
                            controller: _emailController,
                            label: 'البريد الإلكتروني',
                            hint: 'أدخل البريد الإلكتروني',
                            prefixIcon: Icons.email,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                // التحقق من صحة البريد الإلكتروني
                                final emailRegex =
                                    RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                if (!emailRegex.hasMatch(value)) {
                                  return 'يرجى إدخال بريد إلكتروني صحيح';
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // رقم الهاتف
                          AkTextInput(
                            controller: _phoneController,
                            label: 'رقم الهاتف',
                            hint: 'أدخل رقم الهاتف',
                            prefixIcon: Icons.phone,
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // صورة المستخدم (اختياري)
                          _buildImageSection(),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // الصلاحيات والإعدادات
                  AkCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الصلاحيات والإعدادات',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // الدور
                          Builder(
                            builder: (context) {
                              // التحقق من وجود القيمة المحددة في قائمة الأدوار
                              final roleExists = _roles.any(
                                  (role) => role['value'] == _selectedRole);

                              // إذا كانت القيمة المحددة غير موجودة، نعين قيمة افتراضية
                              if (!roleExists && _roles.isNotEmpty) {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  if (mounted) {
                                    setState(() {
                                      _selectedRole = _roles.first['value'];
                                      AppLogger.info(
                                          'تم تعيين دور افتراضي: ${_roles.first['value']}');
                                    });
                                  }
                                });

                                // عرض حقل نصي بدلاً من القائمة المنسدلة حتى يتم تحديث القيمة
                                return TextFormField(
                                  initialValue: 'جاري تحميل الأدوار...',
                                  decoration: InputDecoration(
                                    labelText: 'الدور',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    prefixIcon: const Icon(Icons.badge),
                                  ),
                                  enabled: false,
                                );
                              }

                              // إذا كانت القائمة فارغة، نعرض رسالة
                              if (_roles.isEmpty) {
                                return TextFormField(
                                  initialValue: 'لا توجد أدوار متاحة',
                                  decoration: InputDecoration(
                                    labelText: 'الدور',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    prefixIcon: const Icon(Icons.badge),
                                  ),
                                  enabled: false,
                                );
                              }

                              return DropdownButtonFormField<String>(
                                value: _selectedRole,
                                decoration: InputDecoration(
                                  labelText: 'الدور',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  prefixIcon: const Icon(Icons.badge),
                                ),
                                items: _roles.map((role) {
                                  return DropdownMenuItem<String>(
                                    value: role['value'],
                                    child: Text(role['label']),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedRole = value;
                                    });
                                  }
                                },
                                validator: (value) => null,
                                hint: const Text('اختر الدور'),
                              );
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // مجموعة المستخدمين
                          Builder(
                            builder: (context) {
                              // التحقق من وجود مجموعات
                              if (_userPresenter.groups.isEmpty) {
                                return Container(
                                  padding: const EdgeInsets.all(
                                      Layout.defaultSpacing),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColors.lightTextSecondary),
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  child: const Row(
                                    children: [
                                      Icon(Icons.info,
                                          color: AppColors.warning),
                                      SizedBox(width: 8),
                                      Text('لا توجد مجموعات مستخدمين متاحة'),
                                    ],
                                  ),
                                );
                              }

                              // التحقق من وجود القيمة المحددة في قائمة المجموعات
                              final groupExists = _selectedGroupId == null
                                  ? false
                                  : _userPresenter.groups.any(
                                      (group) => group.id == _selectedGroupId);

                              // إذا كانت القيمة المحددة غير موجودة، نعين قيمة افتراضية
                              if (_selectedGroupId != null && !groupExists) {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  if (mounted) {
                                    setState(() {
                                      _selectedGroupId =
                                          _userPresenter.groups.first.id;
                                      AppLogger.info(
                                          'تم تعيين مجموعة مستخدمين افتراضية: ${_userPresenter.groups.first.name}');
                                    });
                                  }
                                });

                                // عرض حقل نصي بدلاً من القائمة المنسدلة حتى يتم تحديث القيمة
                                return TextFormField(
                                  initialValue:
                                      'جاري تحميل مجموعات المستخدمين...',
                                  decoration: InputDecoration(
                                    labelText: 'مجموعة المستخدمين',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    prefixIcon: const Icon(Icons.group),
                                  ),
                                  enabled: false,
                                );
                              }

                              return DropdownButtonFormField<String>(
                                value: groupExists ? _selectedGroupId : null,
                                decoration: InputDecoration(
                                  labelText: 'مجموعة المستخدمين',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  prefixIcon: const Icon(Icons.group),
                                ),
                                items: _userPresenter.groups.map((group) {
                                  return DropdownMenuItem<String>(
                                    value: group.id,
                                    child: Text(group.name),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedGroupId = value;
                                  });
                                },
                                // إضافة خاصية لجعل القائمة المنسدلة اختيارية
                                validator: (value) => null,
                                // إضافة قيمة افتراضية في حالة عدم وجود قيمة محددة
                                hint: const Text('اختر مجموعة المستخدمين'),
                              );
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // الفرع
                          Builder(
                            builder: (context) {
                              // التحقق من وجود فروع
                              if (_userPresenter.branches.isEmpty) {
                                return Container(
                                  padding: const EdgeInsets.all(
                                      AppDimensions.spacing16),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColors.lightTextSecondary),
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  child: const Row(
                                    children: [
                                      Icon(Icons.info,
                                          color: AppColors.warning),
                                      SizedBox(width: 8),
                                      Text('لا توجد فروع متاحة'),
                                    ],
                                  ),
                                );
                              }

                              // التحقق من وجود القيمة المحددة في قائمة الفروع
                              final branchExists = _selectedBranchId == null
                                  ? false
                                  : _userPresenter.branches.any((branch) =>
                                      branch.id == _selectedBranchId);

                              // إذا كانت القيمة المحددة غير موجودة، نعين قيمة افتراضية
                              if (_selectedBranchId != null && !branchExists) {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  if (mounted) {
                                    setState(() {
                                      _selectedBranchId =
                                          _userPresenter.branches.first.id;
                                      AppLogger.info(
                                          'تم تعيين فرع افتراضي: ${_userPresenter.branches.first.name}');
                                    });
                                  }
                                });

                                // عرض حقل نصي بدلاً من القائمة المنسدلة حتى يتم تحديث القيمة
                                return TextFormField(
                                  initialValue: 'جاري تحميل الفروع...',
                                  decoration: InputDecoration(
                                    labelText: 'الفرع',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    prefixIcon: const Icon(Icons.business),
                                  ),
                                  enabled: false,
                                );
                              }

                              return DropdownButtonFormField<String>(
                                value: branchExists ? _selectedBranchId : null,
                                decoration: InputDecoration(
                                  labelText: 'الفرع',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  prefixIcon: const Icon(Icons.business),
                                ),
                                items: _userPresenter.branches.map((branch) {
                                  return DropdownMenuItem<String>(
                                    value: branch.id,
                                    child: Text(branch.name),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedBranchId = value;
                                  });
                                },
                                validator: (value) => null,
                                hint: const Text('اختر الفرع'),
                              );
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // نوع الوصول للفروع
                          AkDropdownInput<String>(
                            label: 'نوع الوصول للفروع',
                            value: _selectedBranchAccessType,
                            hint: 'اختر نوع الوصول للفروع',
                            prefixIcon: Icons.security,
                            isRequired: true,
                            items:
                                BranchAccessType.getDropdownItems().map((item) {
                              return DropdownMenuItem<String>(
                                value: item['value'],
                                child: Text(
                                  item['label']!,
                                  style: AppTypography(
                                    color: DynamicColors.textPrimary(context),
                                    fontSize: AppTypography.fontSizeMedium,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedBranchAccessType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار نوع الوصول للفروع';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),

                          // حالة النشاط
                          SwitchListTile(
                            title: const Text('نشط'),
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spacing24),

                  // زر الحفظ
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveUser,
                      child: _isLoading
                          ? const CircularProgressIndicator(
                              color: AppColors.onPrimary)
                          : Text(
                              _isNewUser ? 'إضافة المستخدم' : 'حفظ التغييرات'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// حفظ المستخدم
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // معالجة الصورة إذا تم اختيار صورة جديدة
      String? avatarPath;
      if (_selectedImage != null) {
        // حفظ الصورة باستخدام ImageStorageService الجديد
        final userId = widget.user?.id ?? const Uuid().v4();
        avatarPath = await _imageStorageService.saveImage(
          _selectedImage!,
          ImageType.userAvatar,
          userId,
          compressBeforeUpload: true,
          quality: 85,
          maxWidth: 800,
          maxHeight: 800,
        );

        if (avatarPath == null) {
          throw Exception('فشل في حفظ الصورة');
        }
      } else if (_currentImageUrl != null && _currentImageUrl!.isNotEmpty) {
        // الاحتفاظ بالصورة الحالية إذا لم يتم تغييرها
        avatarPath = _currentImageUrl;
      }

      // إنشاء كائن المستخدم (محسن لتشمل معرف الفرع والصورة)
      final user = User(
        id: widget.user?.id,
        username: _usernameController.text,
        password: _isNewUser
            ? _passwordController.text
            : (_passwordController.text.isNotEmpty
                ? _passwordController.text
                : widget.user!
                    .password), // استخدام كلمة المرور الجديدة إذا تم إدخالها، وإلا احتفظ بالقديمة
        fullName: _fullNameController.text,
        roleId: _selectedRole,
        userGroupId: _selectedGroupId, // إضافة معرف مجموعة المستخدمين
        branchId: _selectedBranchId, // إضافة معرف الفرع
        branchAccessType: _selectedBranchAccessType, // إضافة نوع الوصول للفروع
        email: _emailController.text.isEmpty ? null : _emailController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        avatar: avatarPath, // إضافة مسار الصورة
        isActive: _isActive,
      );

      bool success;
      if (_isNewUser) {
        success = await _userPresenter.addUser(user);
      } else {
        success = await _userPresenter.updateUser(user);
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(_isNewUser
                  ? 'تم إضافة المستخدم بنجاح'
                  : 'تم تحديث المستخدم بنجاح')),
        );
        Navigator.pop(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(_isNewUser
                  ? 'فشل في إضافة المستخدم'
                  : 'فشل في تحديث المستخدم')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// بناء قسم رفع الصورة
  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صورة المستخدم (اختياري)',
          style: AppTypography(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing12),

        // منطقة عرض الصورة
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: DynamicColors.border(context),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(12),
            color: DynamicColors.surface(context),
          ),
          child: InkWell(
            onTap: _showImagePickerOptions,
            borderRadius: BorderRadius.circular(12),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.file(
                      _selectedImage!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  )
                : _currentImageUrl != null && _currentImageUrl!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          _currentImageUrl!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildImagePlaceholder();
                          },
                        ),
                      )
                    : _buildImagePlaceholder(),
          ),
        ),

        // أزرار إدارة الصورة
        if (_selectedImage != null ||
            (_currentImageUrl != null && _currentImageUrl!.isNotEmpty))
          Padding(
            padding: const EdgeInsets.only(top: AppDimensions.spacing12),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showImagePickerOptions,
                    icon: const Icon(Icons.edit, size: 18),
                    label: const Text('تغيير الصورة'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: DynamicColors.primary,
                      side: BorderSide(color: DynamicColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacing12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _removeImage,
                    icon: const Icon(Icons.delete, size: 18),
                    label: const Text('إزالة الصورة'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.error,
                      side: const BorderSide(color: AppColors.error),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء عنصر نائب للصورة
  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_a_photo,
          size: 48,
          color: DynamicColors.textSecondary(context),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Text(
          'اضغط لإضافة صورة',
          style: AppTypography(
            fontSize: 14,
            color: DynamicColors.textSecondary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing4),
        Text(
          '(اختياري)',
          style: AppTypography(
            fontSize: 12,
            color: DynamicColors.textSecondary(context),
          ),
        ),
      ],
    );
  }

  /// عرض خيارات اختيار الصورة
  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: AppDimensions.cardPaddingLarge,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'اختيار صورة',
                style: AppTypography(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // خيار الكاميرا
              ListTile(
                leading: Icon(
                  Icons.camera_alt,
                  color: DynamicColors.primary,
                ),
                title: const Text('التقاط صورة'),
                onTap: () {
                  Navigator.pop(context);
                  _captureImageFromCamera();
                },
              ),

              // خيار المعرض
              ListTile(
                leading: Icon(
                  Icons.photo_library,
                  color: DynamicColors.primary,
                ),
                title: const Text('اختيار من المعرض'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),

              const SizedBox(height: AppDimensions.spacing8),
            ],
          ),
        );
      },
    );
  }

  /// التقاط صورة من الكاميرا
  Future<void> _captureImageFromCamera() async {
    try {
      final File? capturedFile = await _imageService.captureImage(
        maxWidth: 800,
        maxHeight: 800,
        quality: 85,
      );

      if (capturedFile != null && mounted) {
        setState(() {
          _selectedImage = capturedFile;
          _currentImageUrl = null; // مسح الصورة السابقة
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في التقاط الصورة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    try {
      final File? pickedFile = await _imageService.pickImage(
        maxWidth: 800,
        maxHeight: 800,
        quality: 85,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          _selectedImage = pickedFile;
          _currentImageUrl = null; // مسح الصورة السابقة
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// إزالة الصورة
  void _removeImage() {
    setState(() {
      _selectedImage = null;
      _currentImageUrl = null;
    });
  }
}
