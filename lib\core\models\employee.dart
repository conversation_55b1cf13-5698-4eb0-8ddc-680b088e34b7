import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'employee_status.dart';

/// نموذج الموظف الموحد
/// تم توحيده من جميع نماذج الموظفين في المشروع
class Employee extends BaseModel {
  // معلومات أساسية
  final String employeeId;
  final String fullName;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? position;
  final String? department;

  // للتوافق مع الكود القديم
  String get employeeNumber => employeeId;
  String? get positionName => position;
  String? get departmentName => department;

  // معلومات الارتباط
  final String? branchId;
  final String? branchName;
  final String? managerId;
  final String? managerName;
  final String? userId;

  // معلومات الحالة
  final bool isActive;
  final String status; // active, inactive, on_leave, terminated

  /// حالة الموظف كـ enum
  EmployeeStatus get employeeStatus => getEmployeeStatusFromString(status);
  final DateTime? hireDate;
  final DateTime? terminationDate;

  // معلومات الراتب
  final double? basicSalary;
  final String? salaryType; // monthly, hourly, daily, weekly
  final double? hourlyRate;
  final Map<String, dynamic>? allowances;
  final Map<String, dynamic>? deductions;

  // معلومات إضافية
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? nationalId;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? maritalStatus;
  final String? emergencyContact;
  final String? emergencyPhone;
  final String? bankName;
  final String? bankAccount;
  final String? bankIban;
  final Map<String, dynamic>? metadata;

  Employee({
    String? id,
    required this.employeeId,
    required this.fullName,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.position,
    this.department,
    this.branchId,
    this.branchName,
    this.managerId,
    this.managerName,
    this.userId,
    this.isActive = true,
    this.status = 'active',
    this.hireDate,
    this.terminationDate,
    this.basicSalary,
    this.salaryType = 'monthly',
    this.hourlyRate,
    this.allowances,
    this.deductions,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.nationalId,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.emergencyContact,
    this.emergencyPhone,
    this.bankName,
    this.bankAccount,
    this.bankIban,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا الموظف مع استبدال الحقول المحددة بقيم جديدة
  Employee copyWith({
    String? id,
    String? employeeId,
    String? fullName,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? position,
    String? department,
    String? branchId,
    String? branchName,
    String? managerId,
    String? managerName,
    String? userId,
    bool? isActive,
    String? status,
    DateTime? hireDate,
    DateTime? terminationDate,
    double? basicSalary,
    String? salaryType,
    double? hourlyRate,
    Map<String, dynamic>? allowances,
    Map<String, dynamic>? deductions,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    String? nationalId,
    DateTime? dateOfBirth,
    String? gender,
    String? maritalStatus,
    String? emergencyContact,
    String? emergencyPhone,
    String? bankName,
    String? bankAccount,
    String? bankIban,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Employee(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      fullName: fullName ?? this.fullName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      position: position ?? this.position,
      department: department ?? this.department,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      userId: userId ?? this.userId,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      basicSalary: basicSalary ?? this.basicSalary,
      salaryType: salaryType ?? this.salaryType,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      allowances: allowances ?? this.allowances,
      deductions: deductions ?? this.deductions,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      nationalId: nationalId ?? this.nationalId,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      bankName: bankName ?? this.bankName,
      bankAccount: bankAccount ?? this.bankAccount,
      bankIban: bankIban ?? this.bankIban,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الموظف إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'full_name': fullName,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'position': position,
      'department': department,
      'branch_id': branchId,
      'branch_name': branchName,
      'manager_id': managerId,
      'manager_name': managerName,
      'user_id': userId,
      'is_active': isActive ? 1 : 0,
      'status': status,
      'hire_date': hireDate?.toIso8601String(),
      'termination_date': terminationDate?.toIso8601String(),
      'basic_salary': basicSalary,
      'salary_type': salaryType,
      'hourly_rate': hourlyRate,
      'allowances': allowances != null ? jsonEncode(allowances) : null,
      'deductions': deductions != null ? jsonEncode(deductions) : null,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'national_id': nationalId,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'marital_status': maritalStatus,
      'emergency_contact': emergencyContact,
      'emergency_phone': emergencyPhone,
      'bank_name': bankName,
      'bank_account': bankAccount,
      'bank_iban': bankIban,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء موظف من Map
  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'],
      employeeId: map['employee_id'] ?? '',
      fullName: map['full_name'] ?? '',
      firstName: map['first_name'],
      lastName: map['last_name'],
      email: map['email'],
      phone: map['phone'],
      position: map['position'],
      department: map['department'],
      branchId: map['branch_id'],
      branchName: map['branch_name'],
      managerId: map['manager_id'],
      managerName: map['manager_name'],
      userId: map['user_id'],
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      status: map['status'] ?? 'active',
      hireDate:
          map['hire_date'] != null ? DateTime.parse(map['hire_date']) : null,
      terminationDate: map['termination_date'] != null
          ? DateTime.parse(map['termination_date'])
          : null,
      basicSalary: map['basic_salary'] is int
          ? (map['basic_salary'] as int).toDouble()
          : (map['basic_salary'] as double?),
      salaryType: map['salary_type'] ?? 'monthly',
      hourlyRate: map['hourly_rate'] is int
          ? (map['hourly_rate'] as int).toDouble()
          : (map['hourly_rate'] as double?),
      allowances: map['allowances'] != null
          ? (map['allowances'] is String
              ? jsonDecode(map['allowances'])
              : map['allowances'] as Map<String, dynamic>)
          : null,
      deductions: map['deductions'] != null
          ? (map['deductions'] is String
              ? jsonDecode(map['deductions'])
              : map['deductions'] as Map<String, dynamic>)
          : null,
      address: map['address'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      nationalId: map['national_id'],
      dateOfBirth: map['date_of_birth'] != null
          ? DateTime.parse(map['date_of_birth'])
          : null,
      gender: map['gender'],
      maritalStatus: map['marital_status'],
      emergencyContact: map['emergency_contact'],
      emergencyPhone: map['emergency_phone'],
      bankName: map['bank_name'],
      bankAccount: map['bank_account'],
      bankIban: map['bank_iban'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تحويل الموظف إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء موظف من JSON
  factory Employee.fromJson(String source) =>
      Employee.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'Employee(id: $id, employeeId: $employeeId, fullName: $fullName, position: $position)';
  }
}
