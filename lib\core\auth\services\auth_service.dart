import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/database/database_helper.dart';
import '../../../features/users/models/user.dart';
import '../../../features/users/services/activity_log_service.dart';
import '../../../features/branches/models/branch.dart';
import '../../../features/branches/services/branch_service.dart';
import '../enums/branch_access_type.dart';

/// خدمة المصادقة
class AuthService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final ActivityLogService _activityLogService = ActivityLogService();
  final BranchService _branchService = BranchService();

  // مفاتيح التخزين الآمن
  static const String _userIdKey = 'user_id';
  static const String _userDataKey = 'user_data';
  static const String _branchIdKey = 'branch_id';

  /// تسجيل الدخول باستخدام اسم المستخدم أو البريد الإلكتروني
  Future<User?> login(String usernameOrEmail, String password,
      {String? branchId}) async {
    try {
      AppLogger.info('🔐 بدء عملية تسجيل الدخول للمستخدم: $usernameOrEmail');
      final db = await _databaseHelper.database;

      // تنظيف البيانات المدخلة
      final cleanUsername = usernameOrEmail.trim();
      final cleanPassword = password.trim();

      // التحقق مما إذا كان المدخل هو بريد إلكتروني
      final bool isEmail = cleanUsername.contains('@');
      AppLogger.info(
          '🔍 نوع تسجيل الدخول: ${isEmail ? 'بريد إلكتروني' : 'اسم مستخدم'}');

      // البحث عن المستخدم أولاً بدون كلمة المرور للتشخيص
      List<Map<String, dynamic>> userCheck;
      if (isEmail) {
        userCheck = await db.query(
          'users',
          where: 'email = ? AND is_deleted = 0',
          whereArgs: [cleanUsername],
        );
      } else {
        userCheck = await db.query(
          'users',
          where: 'username = ? AND is_deleted = 0',
          whereArgs: [cleanUsername],
        );
      }

      if (userCheck.isEmpty) {
        AppLogger.warning('❌ لم يتم العثور على مستخدم بالاسم: $cleanUsername');
        return null;
      }

      final foundUser = userCheck.first;
      AppLogger.info('✅ تم العثور على المستخدم: ${foundUser['username']}');

      // تشفير كلمة المرور المدخلة
      final hashedPassword = _hashPassword(cleanPassword);
      AppLogger.info(
          '🔐 كلمة المرور المحفوظة: ${foundUser['password']?.substring(0, 10)}...');
      AppLogger.info(
          '🔐 كلمة المرور المدخلة (مشفرة): ${hashedPassword.substring(0, 10)}...');

      // التحقق من تطابق كلمة المرور
      if (foundUser['password'] != hashedPassword) {
        AppLogger.warning('❌ كلمة المرور غير صحيحة للمستخدم: $cleanUsername');
        return null;
      }

      // إنشاء كائن المستخدم
      final user = User.fromMap(foundUser);

      // التحقق من أن المستخدم نشط
      if (!user.isActive) {
        AppLogger.warning(
            '⚠️ محاولة تسجيل دخول لمستخدم غير نشط: ${user.username}');
        return null;
      }

      // التحقق من صلاحية الوصول للفرع إذا تم تحديد فرع
      if (branchId != null) {
        final canAccess = await _canAccessBranch(user, branchId);
        if (!canAccess) {
          AppLogger.warning(
              '❌ المستخدم ${user.username} لا يملك صلاحية الوصول للفرع: $branchId');
          return null; // رفض تسجيل الدخول
        }
        AppLogger.info('✅ تم التحقق من صلاحية الوصول للفرع: $branchId');
      }

      // حفظ معلومات المستخدم في التخزين الآمن
      await _saveUserSession(user, branchId: branchId);

      // تسجيل نشاط تسجيل الدخول
      await _activityLogService.logActivity(
        userId: user.id,
        userName: user.username,
        action: 'login',
        module: 'auth',
        details:
            'تسجيل الدخول للمستخدم ${user.username}${branchId != null ? ' في الفرع $branchId' : ''}',
      );

      AppLogger.info('✅ تم تسجيل الدخول بنجاح للمستخدم: ${user.username}');
      return user;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تسجيل الدخول',
        error: e,
        stackTrace: stackTrace,
        context: {
          'usernameOrEmail': usernameOrEmail,
          'branchId': branchId,
        },
      );
      AppLogger.error('❌ فشل في تسجيل الدخول: $e');
      return null;
    }
  }

  /// تسجيل الخروج
  Future<bool> logout() async {
    try {
      // الحصول على معلومات المستخدم قبل تسجيل الخروج
      final currentUser = await getCurrentUser();

      // حذف معلومات المستخدم من التخزين الآمن
      await _secureStorage.delete(key: _userIdKey);
      await _secureStorage.delete(key: _userDataKey);
      await _secureStorage.delete(key: _branchIdKey);

      // تسجيل نشاط تسجيل الخروج
      if (currentUser != null) {
        await _activityLogService.logActivity(
          userId: currentUser.id,
          userName: currentUser.username,
          action: 'logout',
          module: 'auth',
          details: 'تسجيل الخروج للمستخدم ${currentUser.username}',
        );
      }

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تسجيل الخروج',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في تسجيل الخروج: $e');
      return false;
    }
  }

  /// التحقق من حالة تسجيل الدخول
  Future<bool> isLoggedIn() async {
    try {
      final userId = await _secureStorage.read(key: _userIdKey);
      return userId != null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في التحقق من حالة تسجيل الدخول',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في التحقق من حالة تسجيل الدخول: $e');
      return false;
    }
  }

  /// الحصول على المستخدم الحالي
  Future<User?> getCurrentUser() async {
    try {
      final userData = await _secureStorage.read(key: _userDataKey);

      if (userData != null) {
        return User.fromJson(userData);
      }

      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المستخدم الحالي',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في الحصول على المستخدم الحالي: $e');
      return null;
    }
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(User user) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
      final existingUser = await db.query(
        'users',
        where: 'username = ?',
        whereArgs: [user.username],
      );

      if (existingUser.isNotEmpty) {
        return false;
      }

      // تشفير كلمة المرور
      final userWithHashedPassword = user.copyWith(
        password: _hashPassword(user.password),
      );

      // إدخال المستخدم الجديد
      await db.insert('users', userWithHashedPassword.toMap());

      // تسجيل نشاط إنشاء المستخدم
      await _activityLogService.logActivity(
        userId:
            'system', // استخدام معرف النظام لأن المستخدم الجديد لم يسجل الدخول بعد
        userName: 'النظام',
        action: 'create_user',
        module: 'auth',
        details: 'تم إنشاء مستخدم جديد: ${user.username}',
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء مستخدم جديد',
        error: e,
        stackTrace: stackTrace,
        context: {
          'user': user.toMap(),
        },
      );
      return false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
      final existingUser = await db.query(
        'users',
        where: 'username = ? AND id != ?',
        whereArgs: [user.username, user.id],
      );

      if (existingUser.isNotEmpty) {
        return false;
      }

      // تحديث المستخدم
      await db.update(
        'users',
        user.toMap(),
        where: 'id = ?',
        whereArgs: [user.id],
      );

      // تحديث معلومات المستخدم في التخزين الآمن إذا كان المستخدم الحالي
      final currentUser = await getCurrentUser();
      if (currentUser != null && currentUser.id == user.id) {
        await _saveUserSession(user);
      }

      // تسجيل نشاط تحديث المستخدم
      final loggedInUser = currentUser ?? user;
      await _activityLogService.logActivity(
        userId: loggedInUser.id,
        userName: loggedInUser.username,
        action: 'update_user',
        module: 'auth',
        details: 'تم تحديث بيانات المستخدم: ${user.username}',
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'user': user.toMap(),
        },
      );
      return false;
    }
  }

  /// تغيير كلمة المرور
  Future<bool> changePassword(
      String userId, String currentPassword, String newPassword) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من كلمة المرور الحالية
      final result = await db.query(
        'users',
        where: 'id = ? AND password = ?',
        whereArgs: [userId, _hashPassword(currentPassword)],
      );

      if (result.isEmpty) {
        return false;
      }

      // تحديث كلمة المرور
      await db.update(
        'users',
        {
          'password': _hashPassword(newPassword),
          'updated_at': DateTime.now().millisecondsSinceEpoch
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      // الحصول على معلومات المستخدم
      final userResult = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (userResult.isNotEmpty) {
        final user = User.fromMap(userResult.first);

        // تسجيل نشاط تغيير كلمة المرور
        await _activityLogService.logActivity(
          userId: userId,
          userName: user.username,
          action: 'change_password',
          module: 'auth',
          details: 'تم تغيير كلمة المرور للمستخدم ${user.username}',
        );
      }

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تغيير كلمة المرور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
        },
      );
      return false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(String userId) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على معلومات المستخدم قبل الحذف
      final userResult = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      String username = 'غير معروف';
      if (userResult.isNotEmpty) {
        final user = User.fromMap(userResult.first);
        username = user.username;
      }

      // حذف المستخدم
      await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      // تسجيل نشاط حذف المستخدم
      final currentUser = await getCurrentUser();
      await _activityLogService.logActivity(
        userId: currentUser?.id ?? 'system',
        userName: currentUser?.username ?? 'النظام',
        action: 'delete_user',
        module: 'auth',
        details: 'تم حذف المستخدم: $username (معرف: $userId)',
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
        },
      );
      return false;
    }
  }

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query('users');

      return result.map((map) => User.fromMap(map)).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع المستخدمين',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// تشفير كلمة المرور - دالة موحدة للتطبيق
  String _hashPassword(String? password) {
    if (password == null || password.isEmpty) {
      throw ArgumentError('كلمة المرور لا يمكن أن تكون فارغة');
    }
    // تنظيف كلمة المرور من المسافات الزائدة
    final cleanPassword = password.trim();
    final bytes = utf8.encode(cleanPassword);
    final digest = sha256.convert(bytes);
    final hashedPassword = digest.toString();

    AppLogger.info(
        '🔐 تشفير كلمة المرور: ${cleanPassword.length} حرف -> ${hashedPassword.substring(0, 10)}...');
    return hashedPassword;
  }

  /// دالة عامة لتشفير كلمة المرور - للاستخدام في أجزاء أخرى من التطبيق
  static String hashPassword(String? password) {
    if (password == null || password.isEmpty) {
      throw ArgumentError('كلمة المرور لا يمكن أن تكون فارغة');
    }
    // تنظيف كلمة المرور من المسافات الزائدة
    final cleanPassword = password.trim();
    final bytes = utf8.encode(cleanPassword);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// حفظ جلسة المستخدم
  Future<void> _saveUserSession(User user, {String? branchId}) async {
    await _secureStorage.write(key: _userIdKey, value: user.id);
    await _secureStorage.write(key: _userDataKey, value: user.toJson());

    // حفظ معرف الفرع إذا تم تحديده
    if (branchId != null) {
      await _secureStorage.write(key: _branchIdKey, value: branchId);
    }
  }

  /// التحقق من وجود مستخدم مسؤول
  Future<bool> hasAdminUser() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'users',
        where: 'role_id = ?',
        whereArgs: ['admin'],
      );

      return result.isNotEmpty;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في التحقق من وجود مستخدم مسؤول',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// إنشاء مستخدم مسؤول افتراضي (تم تعطيله - يتم في BasicDataInitializer)
  Future<bool> createDefaultAdmin() async {
    AppLogger.info('تم تعطيل إنشاء المستخدم المسؤول الافتراضي في AuthService');
    AppLogger.info('يتم تهيئة المستخدمين في BasicDataInitializer فقط');

    // لا نقوم بأي عملية هنا لتجنب التداخل مع BasicDataInitializer
    return true;
  }

  /// الحصول على الفرع الحالي
  Future<String?> getCurrentBranchId() async {
    try {
      return await _secureStorage.read(key: _branchIdKey);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على الفرع الحالي',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في الحصول على الفرع الحالي: $e');
      return null;
    }
  }

  /// تعيين الفرع الحالي
  Future<bool> setCurrentBranch(String branchId) async {
    try {
      await _secureStorage.write(key: _branchIdKey, value: branchId);
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تعيين الفرع الحالي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'branchId': branchId,
        },
      );
      AppLogger.error('فشل في تعيين الفرع الحالي: $e');
      return false;
    }
  }

  /// الحصول على قائمة الفروع المسموح للمستخدم الحالي الوصول إليها
  Future<List<Branch>> getAccessibleBranches() async {
    try {
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        AppLogger.warning('⚠️ لا يوجد مستخدم مسجل دخول');
        return [];
      }

      return await _getAccessibleBranches(currentUser);
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على الفروع المسموحة: $e');
      return [];
    }
  }

  /// التحقق من إمكانية عرض قائمة الفروع للمستخدم الحالي
  Future<bool> shouldShowBranchSelector() async {
    try {
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        return true; // عرض قائمة الفروع إذا لم يكن هناك مستخدم مسجل دخول
      }

      return _shouldShowBranchSelector(currentUser);
    } catch (e) {
      AppLogger.error('❌ خطأ في التحقق من عرض قائمة الفروع: $e');
      return true; // عرض قائمة الفروع في حالة الخطأ
    }
  }

  /// الحصول على الفرع الافتراضي للمستخدم الحالي
  Future<String?> getDefaultBranchId() async {
    try {
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        AppLogger.warning('⚠️ لا يوجد مستخدم مسجل دخول');
        return null;
      }

      return await _getDefaultBranchId(currentUser);
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على الفرع الافتراضي: $e');
      return null;
    }
  }

  /// الحصول على جميع الفروع
  Future<List<Map<String, dynamic>>> getAllBranches() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query('branches');
      return result;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع الفروع',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في الحصول على جميع الفروع: $e');
      return [];
    }
  }

  /// التحقق من إمكانية وصول المستخدم لفرع معين
  Future<bool> _canAccessBranch(User user, String targetBranchId) async {
    try {
      final accessType =
          BranchAccessType.fromCode(user.branchAccessType ?? 'single_branch');

      switch (accessType) {
        case BranchAccessType.singleBranch:
          return user.branchId == targetBranchId;

        case BranchAccessType.allBranches:
          return true;

        case BranchAccessType.mainBranchOnly:
          final branch = await _branchService.getBranchById(targetBranchId);
          return branch?.isMain ?? false;

        case BranchAccessType.specificBranches:
          // مؤقتاً نسمح بالوصول، يمكن تطوير هذا لاحقاً
          return true;
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في التحقق من صلاحية الوصول للفرع: $e');
      return false;
    }
  }

  /// الحصول على قائمة الفروع المسموح للمستخدم الوصول إليها
  Future<List<Branch>> _getAccessibleBranches(User user) async {
    try {
      final allBranches =
          await _branchService.getBranches(includeInactive: false);
      final accessType =
          BranchAccessType.fromCode(user.branchAccessType ?? 'single_branch');

      switch (accessType) {
        case BranchAccessType.singleBranch:
          if (user.branchId != null) {
            return allBranches.where((b) => b.id == user.branchId).toList();
          }
          return [];

        case BranchAccessType.allBranches:
          return allBranches;

        case BranchAccessType.mainBranchOnly:
          return allBranches.where((b) => b.isMain).toList();

        case BranchAccessType.specificBranches:
          // مؤقتاً نعيد جميع الفروع، يمكن تطوير هذا لاحقاً
          return allBranches;
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على الفروع المسموحة: $e');
      return [];
    }
  }

  /// التحقق من إمكانية عرض قائمة الفروع
  bool _shouldShowBranchSelector(User user) {
    final accessType =
        BranchAccessType.fromCode(user.branchAccessType ?? 'single_branch');
    return accessType.shouldShowBranchSelector;
  }

  /// الحصول على الفرع الافتراضي للمستخدم
  Future<String?> _getDefaultBranchId(User user) async {
    try {
      final accessType =
          BranchAccessType.fromCode(user.branchAccessType ?? 'single_branch');

      switch (accessType) {
        case BranchAccessType.singleBranch:
          return user.branchId;

        case BranchAccessType.mainBranchOnly:
          final branches = await _branchService.getBranches();
          final mainBranch = branches.where((b) => b.isMain).firstOrNull;
          return mainBranch?.id;

        case BranchAccessType.allBranches:
        case BranchAccessType.specificBranches:
          final branches = await _branchService.getBranches();
          final defaultBranch = branches.where((b) => b.isDefault).firstOrNull;
          return defaultBranch?.id ??
              branches.where((b) => b.isMain).firstOrNull?.id;
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على الفرع الافتراضي: $e');
      return null;
    }
  }
}
