import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/providers/app_providers.dart';
// تم إزالة استيراد uuid لأنه غير مستخدم
import 'package:audioplayers/audioplayers.dart';
import '../../../core/utils/number_formatter.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/models/product.dart';
import '../../../core/models/product_unit.dart';
import '../../../core/models/category.dart';
import '../../../core/models/unit.dart';
import '../presenters/product_presenter.dart';
import '../../categories/presenters/category_presenter.dart';
import '../../units/presenters/unit_presenter.dart';
import '../../accounts/presenters/account_presenter.dart'; // إضافة مقدم الحسابات
import '../../../core/services/image_service.dart';
import '../../../core/services/barcode_service.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

class ProductFormScreen extends StatefulWidget {
  final Product? product;

  const ProductFormScreen({Key? key, this.product}) : super(key: key);

  @override
  State<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late ProductPresenter _productPresenter;
  late CategoryPresenter _categoryPresenter;
  late UnitPresenter _unitPresenter;
  late AccountPresenter _accountPresenter;
  final ImageService _imageService = ImageService();
  final BarcodeService _barcodeService = BarcodeService();
  // تم إزالة متغير _uuid لأنه غير مستخدم

  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _skuController = TextEditingController();
  final _priceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minStockController = TextEditingController();
  final _maxStockController = TextEditingController();

  String? _selectedCategoryId;
  String? _selectedUnitId;
  bool _isActive = true;
  String? _imageUrl;
  File? _imageFile;
  bool _isLoading = false;
  bool _hasExpiry = false;
  DateTime? _expiryDate;
  List<ProductUnit> _subUnits = [];

  // متغيرات الحسابات
  String? _selectedAccountId; // الحساب الرئيسي للمنتج
  String? _selectedInventoryAccountId; // حساب المخزون
  String? _selectedRevenueAccountId; // حساب الإيرادات
  String? _selectedCostAccountId; // حساب التكلفة
  List<Map<String, dynamic>> _accounts = []; // قائمة الحسابات
  bool _isLoadingAccounts = false; // حالة تحميل الحسابات

  @override
  void initState() {
    super.initState();
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _categoryPresenter = AppProviders.getLazyPresenter<CategoryPresenter>(
        () => CategoryPresenter());
    _unitPresenter =
        AppProviders.getLazyPresenter<UnitPresenter>(() => UnitPresenter());
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());

    // تحميل البيانات مع تحديد النوع للجداول الموحدة
    _categoryPresenter.loadCategories(type: 'product');
    _unitPresenter.loadUnits(type: 'product');
    _initializeFields();
    _loadAccounts();
  }

  // دالة لتحميل الحسابات من شجرة الحسابات
  Future<void> _loadAccounts() async {
    setState(() {
      _isLoadingAccounts = true;
    });

    try {
      // تحميل الحسابات من مقدم الحسابات
      await _accountPresenter.loadAccounts();

      setState(() {
        // الحصول على كل الحسابات
        _accounts = _accountPresenter.accounts;

        _isLoadingAccounts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAccounts = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الحسابات: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _initializeFields() {
    if (widget.product != null) {
      _nameController.text = widget.product!.name;
      _descriptionController.text = widget.product!.description ?? '';
      _barcodeController.text = widget.product!.barcode ?? '';
      _skuController.text = widget.product!.sku ?? '';
      _priceController.text = widget.product!.salePrice.toString();
      _costPriceController.text = widget.product!.purchasePrice.toString();
      _quantityController.text = widget.product!.quantity.toString();
      _minStockController.text = widget.product!.minStock.toString();
      _maxStockController.text = widget.product!.maxStock?.toString() ?? '';
      _selectedCategoryId = widget.product!.categoryId;
      _selectedUnitId = widget.product!.unitId;
      _isActive = widget.product!.isActive;
      _imageUrl = widget.product!.imageUrl;

      // تعيين معرفات الحسابات من البيانات الإضافية
      if (widget.product!.metadata != null) {
        _selectedAccountId = widget.product!.metadata!['account_id'] as String?;
        _selectedInventoryAccountId =
            widget.product!.metadata!['inventory_account_id'] as String?;
        _selectedRevenueAccountId =
            widget.product!.metadata!['revenue_account_id'] as String?;
        _selectedCostAccountId =
            widget.product!.metadata!['cost_account_id'] as String?;
      }

      // Inicializar los nuevos campos
      if (widget.product!.metadata != null) {
        _hasExpiry = widget.product!.metadata!['hasExpiry'] as bool? ?? false;
        final expiryDateStr =
            widget.product!.metadata!['expiryDate'] as String?;
        if (expiryDateStr != null) {
          _expiryDate = DateTime.tryParse(expiryDateStr);
        }

        // Inicializar unidades secundarias si existen
        if (widget.product!.metadata!['subUnits'] != null) {
          final subUnitsData =
              widget.product!.metadata!['subUnits'] as List<dynamic>;
          _subUnits = subUnitsData
              .map((data) => ProductUnit.fromMap(data as Map<String, dynamic>))
              .toList();
        }
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _barcodeController.dispose();
    _skuController.dispose();
    _priceController.dispose();
    _costPriceController.dispose();
    _quantityController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: widget.product == null ? 'إضافة منتج' : 'تعديل منتج',
        showBackButton: true,
      ),
      body: AkLoadingOverlay(
        isLoading: _isLoading,
        message: 'جاري الحفظ...',
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildImageSection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildPricingSection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildInventorySection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildCategoryAndUnitSection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildAccountsSection(), // إضافة قسم الحسابات
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildExpirySection(),
                    const SizedBox(height: AppDimensions.spacing24),
                    _buildOptionsSection(),
                  ],
                ),
              ),
              _buildBottomBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات المنتج الأساسية',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسم المنتج*',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء إدخال اسم المنتج';
              }
              return null;
            },
          ),
          const SizedBox(height: AppDimensions.spacing16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'الوصف',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _barcodeController,
                  decoration: const InputDecoration(
                    labelText: 'الباركود',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(Icons.qr_code_scanner),
                onPressed: _scanBarcode,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          TextFormField(
            controller: _skuController,
            decoration: const InputDecoration(
              labelText: 'رقم المنتج (SKU)',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'صورة المنتج',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Center(
            child: GestureDetector(
              onTap: _pickImage,
              child: Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: AppColors.lightTextSecondary,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.lightSurfaceVariant),
                ),
                child: _imageFile != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _imageFile!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : _imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _imageUrl!,
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Center(
                                  child: CircularProgressIndicator(
                                    value: loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                .cumulativeBytesLoaded /
                                            loadingProgress.expectedTotalBytes!
                                        : null,
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(
                                    Icons.error_outline,
                                    color: AppColors.error,
                                    size: 40,
                                  ),
                                );
                              },
                            ),
                          )
                        : const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add_a_photo,
                                  size: 40,
                                  color: AppColors.lightTextSecondary,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'اضغط لإضافة صورة',
                                  style: AppTypography(
                                      color: AppColors.lightTextSecondary),
                                ),
                              ],
                            ),
                          ),
              ),
            ),
          ),
          if (_imageFile != null || _imageUrl != null)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Center(
                child: TextButton.icon(
                  onPressed: _removeImage,
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  label: const Text(
                    'إزالة الصورة',
                    style: AppTypography(color: AppColors.error),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPricingSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأسعار',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: FinancialTextField(
                  controller: _priceController,
                  label: 'سعر البيع',
                  hint: 'أدخل سعر البيع',
                  isRequired: true,
                  prefixIcon: Icons.attach_money,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال سعر البيع';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FinancialTextField(
                  controller: _costPriceController,
                  label: 'سعر التكلفة',
                  hint: 'أدخل سعر التكلفة',
                  isRequired: true,
                  prefixIcon: Icons.money,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال سعر التكلفة';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المخزون',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          FinancialTextField(
            controller: _quantityController,
            label: 'الكمية الحالية',
            hint: 'أدخل الكمية الحالية',
            isRequired: true,
            prefixIcon: Icons.inventory,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء إدخال الكمية الحالية';
              }
              return null;
            },
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: FinancialTextField(
                  controller: _minStockController,
                  label: 'الحد الأدنى للمخزون',
                  hint: 'أدخل الحد الأدنى',
                  prefixIcon: Icons.arrow_downward,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FinancialTextField(
                  controller: _maxStockController,
                  label: 'الحد الأقصى للمخزون',
                  hint: 'أدخل الحد الأقصى',
                  prefixIcon: Icons.arrow_upward,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryAndUnitSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التصنيف والوحدة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: ListenableBuilder(
                  listenable: _categoryPresenter,
                  builder: (context, child) {
                    final categories = _categoryPresenter.categories;
                    return DropdownButtonFormField<String?>(
                      value: _selectedCategoryId,
                      decoration: const InputDecoration(
                        labelText: 'التصنيف*',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('اختر التصنيف'),
                        ),
                        ...categories.map(
                          (category) => DropdownMenuItem<String?>(
                            value: category.id,
                            child: Text(category.name),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCategoryId = value);
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'الرجاء اختيار التصنيف';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add_circle, color: AppColors.success),
                tooltip: 'إضافة تصنيف جديد',
                onPressed: _showAddCategoryDialog,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: ListenableBuilder(
                  listenable: _unitPresenter,
                  builder: (context, child) {
                    final units = _unitPresenter.units;
                    return DropdownButtonFormField<String?>(
                      value: _selectedUnitId,
                      decoration: const InputDecoration(
                        labelText: 'الوحدة الرئيسية*',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('اختر الوحدة'),
                        ),
                        ...units.map(
                          (unit) => DropdownMenuItem<String?>(
                            value: unit.id,
                            child: Text(unit.name),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedUnitId = value;
                          // Limpiar las unidades secundarias si cambia la unidad principal
                          if (_subUnits.isNotEmpty) {
                            _subUnits = [];
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'الرجاء اختيار الوحدة';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add_circle, color: AppColors.success),
                tooltip: 'إضافة وحدة جديدة',
                onPressed: _showAddUnitDialog,
              ),
            ],
          ),
          if (_selectedUnitId != null) ...[
            const SizedBox(height: AppDimensions.spacing16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة وحدة فرعية'),
                    onPressed: _showAddSubUnitDialog,
                  ),
                ),
              ],
            ),
            if (_subUnits.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.spacing16),
              const Text(
                'الوحدات الفرعية:',
                style: AppTypography(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _subUnits.length,
                itemBuilder: (context, index) {
                  final subUnit = _subUnits[index];
                  return ListTile(
                    title: Text(subUnit.unitName ?? ''),
                    subtitle: Text(
                      'معامل التحويل: ${subUnit.conversionFactor} - السعر: ${subUnit.salePrice}',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete, color: AppColors.error),
                      onPressed: () {
                        setState(() {
                          _subUnits.removeAt(index);
                        });
                      },
                    ),
                  );
                },
              ),
            ],
          ],
        ],
      ),
    );
  }

  // دالة لبناء قسم الحسابات
  Widget _buildAccountsSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'الحسابات المحاسبية',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(width: 8),
              if (_isLoadingAccounts)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // الحساب الرئيسي للمنتج
          _buildAccountDropdown(
            label: 'الحساب الرئيسي للمنتج',
            value: _selectedAccountId,
            onChanged: (value) {
              setState(() {
                _selectedAccountId = value;
              });
            },
            filterType: 'asset',
            icon: Icons.account_balance,
            iconColor: AppColors.info,
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // حساب المخزون
          _buildAccountDropdown(
            label: 'حساب المخزون',
            value: _selectedInventoryAccountId,
            onChanged: (value) {
              setState(() {
                _selectedInventoryAccountId = value;
              });
            },
            filterType: 'inventory',
            icon: Icons.inventory,
            iconColor: AppColors.success,
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // حساب الإيرادات
          _buildAccountDropdown(
            label: 'حساب الإيرادات',
            value: _selectedRevenueAccountId,
            onChanged: (value) {
              setState(() {
                _selectedRevenueAccountId = value;
              });
            },
            filterType: 'revenue',
            icon: Icons.trending_up,
            iconColor: AppColors.accent,
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // حساب التكلفة
          _buildAccountDropdown(
            label: 'حساب التكلفة',
            value: _selectedCostAccountId,
            onChanged: (value) {
              setState(() {
                _selectedCostAccountId = value;
              });
            },
            filterType: 'expense',
            icon: Icons.trending_down,
            iconColor: AppColors.error,
          ),

          const SizedBox(height: AppDimensions.spacing8),
          TextButton.icon(
            onPressed: () {
              // هنا يمكن إضافة الانتقال إلى شاشة إضافة حساب جديد
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('الانتقال إلى شاشة إضافة حساب جديد'),
                ),
              );
            },
            icon: const Icon(Icons.add, size: 16),
            label: const Text('إضافة حساب جديد'),
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: const Size(0, 32),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              alignment: Alignment.centerLeft,
            ),
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لبناء قائمة منسدلة للحسابات
  Widget _buildAccountDropdown({
    required String label,
    required String? value,
    required Function(String?) onChanged,
    required String filterType,
    required IconData icon,
    required Color iconColor,
    bool isRequired = false,
  }) {
    // تصفية الحسابات حسب النوع
    final filteredAccounts = _accounts.where((account) {
      final type = account['type']?.toString().toLowerCase() ?? '';
      final subcategory =
          account['subcategory']?.toString().toLowerCase() ?? '';

      switch (filterType) {
        case 'asset':
          return type == 'asset';
        case 'inventory':
          return type == 'asset' && subcategory == 'inventory';
        case 'revenue':
          return type == 'revenue' || type == 'income';
        case 'expense':
          return type == 'expense' || type == 'cost';
        default:
          return true;
      }
    }).toList();

    return DropdownButtonFormField<String?>(
      value: value,
      decoration: InputDecoration(
        labelText: isRequired ? '$label *' : label,
        border: const OutlineInputBorder(),
        prefixIcon: Icon(icon, color: iconColor),
      ),
      items: [
        const DropdownMenuItem<String?>(
          value: null,
          child: Text('اختر الحساب'),
        ),
        ...filteredAccounts.map((account) {
          final accountName = account['name'] as String;
          final accountCode = account['code'] as String?;

          return DropdownMenuItem<String?>(
            value: account['id'].toString(),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    accountName,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (accountCode != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    '#$accountCode',
                    style: const AppTypography(
                      fontSize: 12,
                      color: AppColors.lightTextSecondary,
                    ),
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ],
      onChanged: onChanged,
      validator: isRequired
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء اختيار حساب';
              }
              return null;
            }
          : null,
      isExpanded: true,
    );
  }

  Widget _buildExpirySection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تاريخ الصلاحية',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          SwitchListTile(
            title: const Text('المنتج له تاريخ صلاحية'),
            subtitle:
                const Text('تفعيل هذا الخيار إذا كان المنتج ينتهي صلاحيته'),
            value: _hasExpiry,
            onChanged: (value) {
              setState(() => _hasExpiry = value);
            },
          ),
          if (_hasExpiry)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: InkWell(
                onTap: _selectExpiryDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الصلاحية',
                    border: OutlineInputBorder(),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _expiryDate != null
                            ? '${_expiryDate!.day}/${_expiryDate!.month}/${_expiryDate!.year}'
                            : 'اختر تاريخ الصلاحية',
                      ),
                      const Icon(Icons.calendar_today),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOptionsSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خيارات',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          SwitchListTile(
            title: const Text('نشط'),
            subtitle: const Text('المنتج سيكون متاحًا للبيع'),
            value: _isActive,
            onChanged: (value) {
              setState(() => _isActive = value);
            },
          ),
        ],
      ),
    );
  }

  // Métodos auxiliares para manejar imágenes y códigos de barras
  Future<void> _scanBarcode() async {
    try {
      setState(() => _isLoading = true);

      // Guardar el contexto actual
      final currentContext = context;

      final barcode =
          await _barcodeService.scanBarcode(context: currentContext);

      // Verificar si el widget sigue montado
      if (!mounted) return;

      setState(() => _isLoading = false);

      if (barcode != null) {
        setState(() {
          _barcodeController.text = barcode;
        });

        // تشغيل صوت نجاح المسح
        await AudioPlayer().play(AssetSource('sounds/beep.mp3'));
      }
    } catch (e) {
      // Verificar si el widget sigue montado
      if (!mounted) return;

      setState(() => _isLoading = false);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في قراءة الباركود: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imageService.pickImage();

      if (pickedFile != null) {
        setState(() {
          _imageFile = pickedFile;
          _imageUrl = null; // Limpiar la URL anterior si existe
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _removeImage() {
    setState(() {
      _imageFile = null;
      _imageUrl = null;
    });
  }

  Future<void> _selectExpiryDate() async {
    final initialDate =
        _expiryDate ?? DateTime.now().add(const Duration(days: 30));

    // Guardar el contexto actual
    final currentContext = context;

    final pickedDate = await showDatePicker(
      context: currentContext,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 años
    );

    // Verificar si el widget sigue montado
    if (!mounted) return;

    if (pickedDate != null) {
      setState(() {
        _expiryDate = pickedDate;
      });
    }
  }

  // Diálogos para agregar categorías, unidades y subunidades
  Future<void> _showAddCategoryDialog() async {
    final nameController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    // Guardar el contexto actual
    final currentContext = context;

    final result = await showDialog<bool>(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('إضافة تصنيف جديد'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'اسم التصنيف*',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء إدخال اسم التصنيف';
              }
              return null;
            },
            autofocus: true,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(dialogContext, true);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );

    // Verificar si el widget sigue montado
    if (!mounted) return;

    if (result == true) {
      setState(() => _isLoading = true);

      try {
        // إنشاء كائن Category جديد
        final category = Category(
          name: nameController.text,
          description: '',
          type: 'product',
        );

        final success = await _categoryPresenter.addCategory(category);

        // Verificar si el widget sigue montado
        if (!mounted) return;

        setState(() => _isLoading = false);

        if (success) {
          // Actualizar la lista de categorías
          await _categoryPresenter.init();

          // Verificar si el widget sigue montado
          if (!mounted) return;

          // Mostrar mensaje de éxito
          _showSnackBar('تم إضافة التصنيف بنجاح', isSuccess: true);
        }
      } catch (e) {
        // Verificar si el widget sigue montado
        if (!mounted) return;

        setState(() => _isLoading = false);

        _showSnackBar('فشل في إضافة التصنيف: $e', isSuccess: false);
      }
    }
  }

  /// عرض رسالة في شريط Snackbar
  void _showSnackBar(String message, {bool isSuccess = true}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? AppColors.success : AppColors.error,
      ),
    );
  }

  Future<void> _showAddUnitDialog() async {
    final nameController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    // Guardar el contexto actual
    final currentContext = context;

    final result = await showDialog<bool>(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('إضافة وحدة جديدة'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'اسم الوحدة*',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء إدخال اسم الوحدة';
              }
              return null;
            },
            autofocus: true,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(dialogContext, true);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );

    // Verificar si el widget sigue montado
    if (!mounted) return;

    if (result == true) {
      setState(() => _isLoading = true);

      try {
        // إنشاء كائن Unit جديد
        final unit = Unit(
          name: nameController.text,
          symbol: nameController.text.substring(0, 1).toUpperCase(),
          isBase: true,
        );

        final success = await _unitPresenter.addUnit(unit);

        // Verificar si el widget sigue montado
        if (!mounted) return;

        setState(() => _isLoading = false);

        if (success) {
          // Actualizar la lista de unidades
          await _unitPresenter.init();

          // Verificar si el widget sigue montado
          if (!mounted) return;

          // Mostrar mensaje de éxito
          _showSnackBar('تم إضافة الوحدة بنجاح', isSuccess: true);
        }
      } catch (e) {
        // Verificar si el widget sigue montado
        if (!mounted) return;

        setState(() => _isLoading = false);

        _showSnackBar('فشل في إضافة الوحدة: $e', isSuccess: false);
      }
    }
  }

  Future<void> _showAddSubUnitDialog() async {
    if (_selectedUnitId == null) {
      _showSnackBar('الرجاء اختيار الوحدة الرئيسية أولاً', isSuccess: false);
      return;
    }

    // Obtener la unidad principal seleccionada
    final mainUnit = _unitPresenter.units.firstWhere(
      (unit) => unit.id == _selectedUnitId,
    );

    // Controladores para el formulario
    final nameController = TextEditingController();
    final conversionFactorController = TextEditingController();
    final priceController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    // Guardar el contexto actual
    final currentContext = context;

    // Mostrar diálogo para agregar unidad secundaria
    final result = await showDialog<Map<String, dynamic>>(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: Text('إضافة وحدة فرعية لـ ${mainUnit.name}'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListenableBuilder(
                listenable: _unitPresenter,
                builder: (context, child) {
                  final units = _unitPresenter.units;
                  return DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الوحدة الفرعية*',
                      border: OutlineInputBorder(),
                    ),
                    items: units
                        .where((unit) => unit.id != _selectedUnitId)
                        .map(
                          (unit) => DropdownMenuItem(
                            value: unit.id,
                            child: Text(unit.name),
                          ),
                        )
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        final selectedUnit = units.firstWhere(
                          (unit) => unit.id == value,
                        );
                        nameController.text = selectedUnit.name;
                      }
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'الرجاء اختيار الوحدة الفرعية';
                      }
                      return null;
                    },
                  );
                },
              ),
              const SizedBox(height: AppDimensions.spacing16),
              TextFormField(
                controller: conversionFactorController,
                decoration: InputDecoration(
                  labelText: 'معامل التحويل*',
                  hintText: 'عدد ${mainUnit.name} في هذه الوحدة',
                  border: const OutlineInputBorder(),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال معامل التحويل';
                  }
                  final factor = double.tryParse(value);
                  if (factor == null || factor <= 0) {
                    return 'الرجاء إدخال رقم صحيح أكبر من 0';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppDimensions.spacing16),
              TextFormField(
                controller: priceController,
                decoration: const InputDecoration(
                  labelText: 'سعر البيع*',
                  border: OutlineInputBorder(),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال سعر البيع';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price < 0) {
                    return 'الرجاء إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(dialogContext, {
                  'unitName': nameController.text,
                  'conversionFactor':
                      double.parse(conversionFactorController.text),
                  'price': double.parse(priceController.text),
                });
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );

    // Verificar si el widget sigue montado
    if (!mounted) return;

    if (result != null) {
      setState(() {
        // Crear nueva unidad secundaria
        final subUnit = ProductUnit(
          unitId: _selectedUnitId,
          unitName: result['unitName'],
          unitSymbol: result['unitSymbol'] ?? '',
          conversionFactor: result['conversionFactor'],
          salePrice: result['price'],
          purchasePrice:
              result['price'] * 0.8, // تقدير سعر الشراء كـ 80% من سعر البيع
        );

        // Agregar a la lista de unidades secundarias
        _subUnits.add(subUnit);
      });

      // Mostrar mensaje de éxito
      _showSnackBar('تم إضافة الوحدة الفرعية بنجاح', isSuccess: true);
    }
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightSurface,
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _submitForm,
              child: Text(widget.product == null ? 'إضافة' : 'تحديث'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // حفظ الصورة إذا تم اختيار صورة جديدة
      String? imageUrl = _imageUrl;
      if (_imageFile != null) {
        imageUrl = await _imageService.saveImageLocally(
          _imageFile!,
          'products',
          compressBeforeUpload: true,
          quality: 85,
        );
      }

      // إنشاء أو تحديث البيانات الوصفية
      final Map<String, dynamic> metadata = widget.product?.metadata ?? {};

      // معالجة تاريخ انتهاء الصلاحية
      if (_hasExpiry && _expiryDate != null) {
        metadata['hasExpiry'] = true;
        metadata['expiryDate'] = _expiryDate!.toIso8601String();
      } else {
        metadata['hasExpiry'] = false;
        metadata.remove('expiryDate');
      }

      // معالجة الوحدات الفرعية
      if (_subUnits.isNotEmpty) {
        // تحويل الوحدات الفرعية إلى JSON
        final subUnitsJson = _subUnits.map((unit) => unit.toJson()).toList();
        metadata['subUnits'] = subUnitsJson;

        // تخزين عدد الوحدات الفرعية للتحقق السريع
        metadata['hasSubUnits'] = true;
        metadata['subUnitsCount'] = _subUnits.length;
      } else {
        metadata['hasSubUnits'] = false;
        metadata.remove('subUnits');
        metadata.remove('subUnitsCount');
      }

      // إضافة معرفات الحسابات إلى البيانات الإضافية
      if (_selectedAccountId != null) {
        metadata['account_id'] = _selectedAccountId;
      }
      if (_selectedInventoryAccountId != null) {
        metadata['inventory_account_id'] = _selectedInventoryAccountId;
      }
      if (_selectedRevenueAccountId != null) {
        metadata['revenue_account_id'] = _selectedRevenueAccountId;
      }
      if (_selectedCostAccountId != null) {
        metadata['cost_account_id'] = _selectedCostAccountId;
      }

      // إنشاء كائن المنتج
      final product = Product(
        id: widget.product?.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        barcode: _barcodeController.text.trim().isEmpty
            ? null
            : _barcodeController.text.trim(),
        sku: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
        salePrice: double.parse(_priceController.text),
        purchasePrice: double.parse(_costPriceController.text),
        quantity: double.parse(_quantityController.text),
        categoryId: _selectedCategoryId,
        unitId: _selectedUnitId,
        minStock: _minStockController.text.isEmpty
            ? 0.0
            : double.tryParse(_minStockController.text) ?? 0.0,
        maxStock: _maxStockController.text.isEmpty
            ? null
            : double.tryParse(_maxStockController.text),
        isActive: _isActive,
        imageUrl: imageUrl,
        metadata: metadata,
        hasExpiry: _hasExpiry,
        expiryDate: _hasExpiry ? _expiryDate : null,
      );

      bool success;
      if (widget.product == null) {
        success = await _productPresenter.addProduct(product);
      } else {
        success = await _productPresenter.updateProduct(product);
      }

      if (!mounted) return;

      setState(() => _isLoading = false);

      // عرض رسالة نجاح أو فشل
      _showSnackBar(
        success
            ? 'تم ${widget.product == null ? 'إضافة' : 'تحديث'} المنتج بنجاح'
            : 'فشل في ${widget.product == null ? 'إضافة' : 'تحديث'} المنتج',
        isSuccess: success,
      );

      // إذا نجحت العملية، العودة إلى الشاشة السابقة
      if (success) {
        // تأخير قصير للسماح للمستخدم برؤية رسالة النجاح
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);

      // تسجيل الخطأ للتتبع
      AppLogger.error('خطأ أثناء حفظ المنتج: $e');

      // عرض رسالة خطأ للمستخدم
      if (!mounted) return;

      _showSnackBar(
        'حدث خطأ أثناء حفظ المنتج: ${e.toString().split('\n').first}',
        isSuccess: false,
      );
    }
  }
}
