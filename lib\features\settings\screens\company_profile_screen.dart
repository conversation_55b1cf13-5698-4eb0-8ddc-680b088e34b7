import 'dart:io';
import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/settings_presenter.dart';

/// شاشة إعدادات الملف الشخصي (الكليشة)
class CompanyProfileScreen extends StatefulWidget {
  const CompanyProfileScreen({Key? key}) : super(key: key);

  @override
  State<CompanyProfileScreen> createState() => _CompanyProfileScreenState();
}

class _CompanyProfileScreenState extends State<CompanyProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _companyAddressController =
      TextEditingController();
  final TextEditingController _companyPhoneController = TextEditingController();
  final TextEditingController _companyEmailController = TextEditingController();
  final TextEditingController _companyWebsiteController =
      TextEditingController();
  final TextEditingController _companyVatNumberController =
      TextEditingController();
  final TextEditingController _companyCommercialRegisterController =
      TextEditingController();
  final TextEditingController _companyTaglineController =
      TextEditingController();
  final TextEditingController _companyFooterController =
      TextEditingController();

  bool _isLoading = false;
  File? _logoFile;
  String? _logoPath;
  bool _showQrCode = true;
  bool _showVatNumber = true;
  bool _showCommercialRegister = true;

  @override
  void initState() {
    super.initState();
    // استخدام addPostFrameCallback لتجنب استدعاء setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSettings();
    });
  }

  /// تحميل إعدادات الملف الشخصي
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
          () => SettingsPresenter());
      await settings.loadSettings();

      // تعبئة الحقول بالإعدادات المحفوظة
      _companyNameController.text = settings.getSetting('company_name') ?? '';
      _companyAddressController.text =
          settings.getSetting('company_address') ?? '';
      _companyPhoneController.text = settings.getSetting('company_phone') ?? '';
      _companyEmailController.text = settings.getSetting('company_email') ?? '';
      _companyWebsiteController.text =
          settings.getSetting('company_website') ?? '';
      _companyVatNumberController.text =
          settings.getSetting('company_vat_number') ?? '';
      _companyCommercialRegisterController.text =
          settings.getSetting('company_commercial_register') ?? '';
      _companyTaglineController.text =
          settings.getSetting('company_tagline') ?? '';
      _companyFooterController.text =
          settings.getSetting('company_footer') ?? '';

      _logoPath = settings.getSetting('company_logo_path');
      _showQrCode = settings.getSetting('show_qr_code') == 'true';
      _showVatNumber = settings.getSetting('show_vat_number') == 'true';
      _showCommercialRegister =
          settings.getSetting('show_commercial_register') == 'true';
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل الإعدادات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// حفظ إعدادات الملف الشخصي
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
          () => SettingsPresenter());

      // حفظ الإعدادات
      await settings.saveSetting('company_name', _companyNameController.text);
      await settings.saveSetting(
          'company_address', _companyAddressController.text);
      await settings.saveSetting('company_phone', _companyPhoneController.text);
      await settings.saveSetting('company_email', _companyEmailController.text);
      await settings.saveSetting(
          'company_website', _companyWebsiteController.text);
      await settings.saveSetting(
          'company_vat_number', _companyVatNumberController.text);
      await settings.saveSetting('company_commercial_register',
          _companyCommercialRegisterController.text);
      await settings.saveSetting(
          'company_tagline', _companyTaglineController.text);
      await settings.saveSetting(
          'company_footer', _companyFooterController.text);

      // حفظ مسار الشعار إذا تم تحديثه
      if (_logoFile != null) {
        // هنا يمكن إضافة كود لنسخ الملف إلى مجلد التطبيق
        // وحفظ المسار الجديد
        final String newLogoPath = _logoFile!.path;
        await settings.saveSetting('company_logo_path', newLogoPath);
      }

      await settings.saveSetting('show_qr_code', _showQrCode.toString());
      await settings.saveSetting('show_vat_number', _showVatNumber.toString());
      await settings.saveSetting(
          'show_commercial_register', _showCommercialRegister.toString());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ إعدادات الملف الشخصي بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ الإعدادات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختيار شعار الشركة
  Future<void> _pickLogo() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _logoFile = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في اختيار الصورة: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    _companyWebsiteController.dispose();
    _companyVatNumberController.dispose();
    _companyCommercialRegisterController.dispose();
    _companyTaglineController.dispose();
    _companyFooterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AkAppBar(
        title: 'إعدادات الملف الشخصي',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الشركة الأساسية
                    AkCard(
                      type: AkCardType.elevated,
                      size: AkCardSize.medium,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'معلومات الشركة الأساسية',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          // شعار الشركة
                          Center(
                            child: Column(
                              children: [
                                Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColors.lightTextSecondary),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: _logoFile != null
                                      ? Image.file(
                                          _logoFile!,
                                          fit: BoxFit.contain,
                                        )
                                      : _logoPath != null
                                          ? Image.file(
                                              File(_logoPath!),
                                              fit: BoxFit.contain,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Center(
                                                  child: Icon(
                                                    Icons.image_not_supported,
                                                    size: 50,
                                                    color: AppColors
                                                        .lightTextSecondary,
                                                  ),
                                                );
                                              },
                                            )
                                          : const Center(
                                              child: Icon(
                                                Icons.add_photo_alternate,
                                                size: 50,
                                                color: AppColors
                                                    .lightTextSecondary,
                                              ),
                                            ),
                                ),
                                const SizedBox(height: 8),
                                ElevatedButton.icon(
                                  onPressed: _pickLogo,
                                  icon: const Icon(Icons.upload),
                                  label: const Text('تحميل شعار الشركة'),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'الحجم المثالي: 400×400 بكسل',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // اسم الشركة
                          AkTextInput(
                            controller: _companyNameController,
                            label: 'اسم الشركة',
                            hint: 'أدخل اسم الشركة',
                            prefixIcon: Icons.business,
                            isRequired: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال اسم الشركة';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          // شعار الشركة النصي
                          AkTextInput(
                            controller: _companyTaglineController,
                            label: 'شعار الشركة النصي',
                            hint: 'أدخل شعار الشركة النصي',
                            prefixIcon: Icons.format_quote,
                          ),
                          const SizedBox(height: 16),
                          // عنوان الشركة
                          AkTextInput(
                            controller: _companyAddressController,
                            label: 'عنوان الشركة',
                            hint: 'أدخل عنوان الشركة',
                            prefixIcon: Icons.location_on,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 16),
                          // رقم الهاتف
                          AkPhoneInput(
                            controller: _companyPhoneController,
                            label: 'رقم الهاتف',
                            hint: 'أدخل رقم الهاتف',
                            defaultCountryCode: '+967',
                          ),
                          const SizedBox(height: 16),
                          // البريد الإلكتروني
                          AkTextInput(
                            controller: _companyEmailController,
                            label: 'البريد الإلكتروني',
                            hint: 'أدخل البريد الإلكتروني',
                            prefixIcon: Icons.email,
                            keyboardType: TextInputType.emailAddress,
                          ),
                          const SizedBox(height: 16),
                          // الموقع الإلكتروني
                          AkTextInput(
                            controller: _companyWebsiteController,
                            label: 'الموقع الإلكتروني',
                            hint: 'أدخل الموقع الإلكتروني',
                            prefixIcon: Icons.language,
                            keyboardType: TextInputType.url,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // المعلومات القانونية
                    AkCard(
                      type: AkCardType.elevated,
                      size: AkCardSize.medium,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المعلومات القانونية',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          // الرقم الضريبي
                          AkTextInput(
                            controller: _companyVatNumberController,
                            label: 'الرقم الضريبي',
                            hint: 'أدخل الرقم الضريبي',
                            prefixIcon: Icons.receipt_long,
                          ),
                          const SizedBox(height: 16),
                          // السجل التجاري
                          AkTextInput(
                            controller: _companyCommercialRegisterController,
                            label: 'السجل التجاري',
                            hint: 'أدخل رقم السجل التجاري',
                            prefixIcon: Icons.assignment,
                          ),
                          const SizedBox(height: 16),
                          // إظهار الرقم الضريبي
                          SwitchListTile(
                            title: const Text('إظهار الرقم الضريبي'),
                            subtitle: const Text(
                                'عرض الرقم الضريبي في المستندات المطبوعة'),
                            value: _showVatNumber,
                            onChanged: (value) {
                              setState(() {
                                _showVatNumber = value;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                          // إظهار السجل التجاري
                          SwitchListTile(
                            title: const Text('إظهار السجل التجاري'),
                            subtitle: const Text(
                                'عرض رقم السجل التجاري في المستندات المطبوعة'),
                            value: _showCommercialRegister,
                            onChanged: (value) {
                              setState(() {
                                _showCommercialRegister = value;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                          // إظهار رمز QR
                          SwitchListTile(
                            title: const Text('إظهار رمز QR'),
                            subtitle:
                                const Text('عرض رمز QR في المستندات المطبوعة'),
                            value: _showQrCode,
                            onChanged: (value) {
                              setState(() {
                                _showQrCode = value;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // نص التذييل
                    AkCard(
                      type: AkCardType.elevated,
                      size: AkCardSize.medium,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'نص التذييل',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          AkTextInput(
                            controller: _companyFooterController,
                            label: 'نص التذييل',
                            hint:
                                'أدخل نص التذييل الذي سيظهر في أسفل المستندات',
                            maxLines: 3,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // أزرار الإجراءات
                    Row(
                      children: [
                        Expanded(
                          child: AkSaveButton(
                            onPressed: _isLoading ? null : _saveSettings,
                            isLoading: _isLoading,
                            text: 'حفظ الإعدادات',
                          ),
                        ),
                        const SizedBox(width: 16),
                        AkButton(
                          onPressed: _isLoading ? null : _showPreview,
                          text: 'معاينة',
                          type: AkButtonType.secondary,
                          icon: Icons.preview,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// عرض معاينة للكليشة
  void _showPreview() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معاينة الكليشة'),
        content: Container(
          width: double.maxFinite,
          constraints: const BoxConstraints(maxHeight: 500),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // شعار الشركة
                if (_logoFile != null || _logoPath != null)
                  Container(
                    width: 100,
                    height: 100,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: _logoFile != null
                        ? Image.file(
                            _logoFile!,
                            fit: BoxFit.contain,
                          )
                        : _logoPath != null
                            ? Image.file(
                                File(_logoPath!),
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      size: 50,
                                      color: AppColors.lightTextSecondary,
                                    ),
                                  );
                                },
                              )
                            : const SizedBox(),
                  ),

                // اسم الشركة
                Text(
                  _companyNameController.text,
                  style: const AppTypography(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                // شعار الشركة النصي
                if (_companyTaglineController.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      _companyTaglineController.text,
                      style: const AppTypography(
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                const Divider(height: 24),

                // معلومات الاتصال
                if (_companyAddressController.text.isNotEmpty)
                  _buildInfoRow(
                      Icons.location_on, _companyAddressController.text),
                if (_companyPhoneController.text.isNotEmpty)
                  _buildInfoRow(Icons.phone, _companyPhoneController.text),
                if (_companyEmailController.text.isNotEmpty)
                  _buildInfoRow(Icons.email, _companyEmailController.text),
                if (_companyWebsiteController.text.isNotEmpty)
                  _buildInfoRow(Icons.language, _companyWebsiteController.text),

                // المعلومات القانونية
                if (_showVatNumber &&
                    _companyVatNumberController.text.isNotEmpty)
                  _buildInfoRow(Icons.receipt_long,
                      'الرقم الضريبي: ${_companyVatNumberController.text}'),
                if (_showCommercialRegister &&
                    _companyCommercialRegisterController.text.isNotEmpty)
                  _buildInfoRow(Icons.assignment,
                      'السجل التجاري: ${_companyCommercialRegisterController.text}'),

                // رمز QR
                if (_showQrCode)
                  Container(
                    width: 100,
                    height: 100,
                    margin: const EdgeInsets.only(top: 16),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.lightTextSecondary),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.qr_code,
                        size: 70,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ),

                // نص التذييل
                if (_companyFooterController.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(
                      _companyFooterController.text,
                      style: const AppTypography(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppColors.lightSurfaceVariant),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.lightTextSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
