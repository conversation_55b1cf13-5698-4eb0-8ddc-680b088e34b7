import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';

/// مقدم خدمة إعدادات التطبيق
class SettingsPresenter extends ChangeNotifier {
  SharedPreferences? _prefs;
  final Map<String, String> _settings = {};
  bool _isLoading = false;
  String? _errorMessage;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تهيئة مقدم الخدمة
  Future<void> init() async {
    _setLoading(true);
    try {
      _prefs = await SharedPreferences.getInstance();
      await loadSettings();
      _errorMessage = null;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل في تهيئة الإعدادات: $e';
      ErrorTracker.captureError(
        'فشل في تهيئة الإعدادات',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الإعدادات
  Future<void> loadSettings() async {
    _setLoading(true);
    try {
      _prefs ??= await SharedPreferences.getInstance();

      // استرجاع جميع المفاتيح
      final keys = _prefs!.getKeys();
      _settings.clear();

      // تحميل الإعدادات من التخزين المحلي
      for (final key in keys) {
        if (key.startsWith('setting_')) {
          final value = _prefs!.getString(key);
          if (value != null) {
            final settingKey = key.replaceFirst('setting_', '');
            _settings[settingKey] = value;
          }
        }
      }

      _errorMessage = null;
      AppLogger.info('تم تحميل الإعدادات بنجاح');
    } catch (e, stackTrace) {
      _errorMessage = 'فشل في تحميل الإعدادات: $e';
      ErrorTracker.captureError(
        'فشل في تحميل الإعدادات',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على قيمة إعداد
  String? getSetting(String key) {
    return _settings[key];
  }

  /// الحصول على قيمة إعداد كرقم
  double? getSettingAsDouble(String key) {
    final value = _settings[key];
    if (value == null) return null;
    return double.tryParse(value);
  }

  /// الحصول على قيمة إعداد كرقم صحيح
  int? getSettingAsInt(String key) {
    final value = _settings[key];
    if (value == null) return null;
    return int.tryParse(value);
  }

  /// الحصول على قيمة إعداد كقيمة منطقية
  bool? getSettingAsBool(String key) {
    final value = _settings[key];
    if (value == null) return null;
    return value.toLowerCase() == 'true';
  }

  /// حفظ إعداد
  Future<bool> saveSetting(String key, String value) async {
    _setLoading(true);
    try {
      _prefs ??= await SharedPreferences.getInstance();

      final success = await _prefs!.setString('setting_$key', value);
      if (success) {
        _settings[key] = value;
        notifyListeners();
        AppLogger.info('تم حفظ الإعداد بنجاح: $key = $value');
      }
      return success;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل في حفظ الإعداد: $e';
      ErrorTracker.captureError(
        'فشل في حفظ الإعداد',
        error: e,
        stackTrace: stackTrace,
        context: {'key': key, 'value': value},
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حفظ إعداد كرقم
  Future<bool> saveSettingDouble(String key, double value) {
    return saveSetting(key, value.toString());
  }

  /// حفظ إعداد كرقم صحيح
  Future<bool> saveSettingInt(String key, int value) {
    return saveSetting(key, value.toString());
  }

  /// حفظ إعداد كقيمة منطقية
  Future<bool> saveSettingBool(String key, bool value) {
    return saveSetting(key, value.toString());
  }

  /// حذف إعداد
  Future<bool> removeSetting(String key) async {
    _setLoading(true);
    try {
      _prefs ??= await SharedPreferences.getInstance();

      final success = await _prefs!.remove('setting_$key');
      if (success) {
        _settings.remove(key);
        notifyListeners();
        AppLogger.info('تم حذف الإعداد بنجاح: $key');
      }
      return success;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل في حذف الإعداد: $e';
      ErrorTracker.captureError(
        'فشل في حذف الإعداد',
        error: e,
        stackTrace: stackTrace,
        context: {'key': key},
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// مسح جميع الإعدادات
  Future<bool> clearSettings() async {
    _setLoading(true);
    try {
      _prefs ??= await SharedPreferences.getInstance();

      // الحصول على جميع المفاتيح
      final keys =
          _prefs!.getKeys().where((key) => key.startsWith('setting_')).toList();

      // حذف كل مفتاح
      for (final key in keys) {
        await _prefs!.remove(key);
      }

      _settings.clear();
      notifyListeners();
      AppLogger.info('تم مسح جميع الإعدادات بنجاح');
      return true;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل في مسح الإعدادات: $e';
      ErrorTracker.captureError(
        'فشل في مسح الإعدادات',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// الحصول على جميع الإعدادات
  Map<String, String> getAllSettings() {
    return Map<String, String>.from(_settings);
  }
}
