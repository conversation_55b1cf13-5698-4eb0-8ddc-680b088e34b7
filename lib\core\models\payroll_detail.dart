import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج تفاصيل الراتب
/// يستخدم لتخزين تفاصيل رواتب الموظفين في كشف الرواتب
class PayrollDetail extends BaseModel {
  // معلومات أساسية
  final String payrollId;
  final String employeeId;
  final String employeeNumber;
  final String employeeName;
  
  // معلومات الراتب
  final double basicSalary;
  final double allowances;
  final double overtime;
  final double bonus;
  final double deductions;
  final double advances;
  final double loans;
  final double taxes;
  final double netSalary;
  
  // معلومات إضافية
  final Map<String, dynamic>? metadata;

  PayrollDetail({
    String? id,
    required this.payrollId,
    required this.employeeId,
    required this.employeeNumber,
    required this.employeeName,
    required this.basicSalary,
    this.allowances = 0.0,
    this.overtime = 0.0,
    this.bonus = 0.0,
    this.deductions = 0.0,
    this.advances = 0.0,
    this.loans = 0.0,
    this.taxes = 0.0,
    required this.netSalary,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من تفاصيل الراتب مع استبدال الحقول المحددة بقيم جديدة
  PayrollDetail copyWith({
    String? id,
    String? payrollId,
    String? employeeId,
    String? employeeNumber,
    String? employeeName,
    double? basicSalary,
    double? allowances,
    double? overtime,
    double? bonus,
    double? deductions,
    double? advances,
    double? loans,
    double? taxes,
    double? netSalary,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return PayrollDetail(
      id: id ?? this.id,
      payrollId: payrollId ?? this.payrollId,
      employeeId: employeeId ?? this.employeeId,
      employeeNumber: employeeNumber ?? this.employeeNumber,
      employeeName: employeeName ?? this.employeeName,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      overtime: overtime ?? this.overtime,
      bonus: bonus ?? this.bonus,
      deductions: deductions ?? this.deductions,
      advances: advances ?? this.advances,
      loans: loans ?? this.loans,
      taxes: taxes ?? this.taxes,
      netSalary: netSalary ?? this.netSalary,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل تفاصيل الراتب إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payroll_id': payrollId,
      'employee_id': employeeId,
      'employee_number': employeeNumber,
      'employee_name': employeeName,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'overtime': overtime,
      'bonus': bonus,
      'deductions': deductions,
      'advances': advances,
      'loans': loans,
      'taxes': taxes,
      'net_salary': netSalary,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تفاصيل راتب من Map
  factory PayrollDetail.fromMap(Map<String, dynamic> map) {
    return PayrollDetail(
      id: map['id'],
      payrollId: map['payroll_id'] ?? '',
      employeeId: map['employee_id'] ?? '',
      employeeNumber: map['employee_number'] ?? '',
      employeeName: map['employee_name'] ?? '',
      basicSalary: map['basic_salary'] is int
          ? (map['basic_salary'] as int).toDouble()
          : (map['basic_salary'] as double? ?? 0.0),
      allowances: map['allowances'] is int
          ? (map['allowances'] as int).toDouble()
          : (map['allowances'] as double? ?? 0.0),
      overtime: map['overtime'] is int
          ? (map['overtime'] as int).toDouble()
          : (map['overtime'] as double? ?? 0.0),
      bonus: map['bonus'] is int
          ? (map['bonus'] as int).toDouble()
          : (map['bonus'] as double? ?? 0.0),
      deductions: map['deductions'] is int
          ? (map['deductions'] as int).toDouble()
          : (map['deductions'] as double? ?? 0.0),
      advances: map['advances'] is int
          ? (map['advances'] as int).toDouble()
          : (map['advances'] as double? ?? 0.0),
      loans: map['loans'] is int
          ? (map['loans'] as int).toDouble()
          : (map['loans'] as double? ?? 0.0),
      taxes: map['taxes'] is int
          ? (map['taxes'] as int).toDouble()
          : (map['taxes'] as double? ?? 0.0),
      netSalary: map['net_salary'] is int
          ? (map['net_salary'] as int).toDouble()
          : (map['net_salary'] as double? ?? 0.0),
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تشفير البيانات الإضافية
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    return metadata.toString();
  }

  /// فك تشفير البيانات الإضافية
  static Map<String, dynamic> _decodeMetadata(String encodedMetadata) {
    // تنفيذ بسيط، يمكن استخدام json.encode/decode في التطبيق الفعلي
    return {'data': encodedMetadata};
  }

  @override
  String toString() {
    return 'PayrollDetail{id: $id, employeeName: $employeeName, netSalary: $netSalary}';
  }
}
