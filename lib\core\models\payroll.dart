import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'payroll_detail.dart';
import 'payroll_status.dart';

/// نموذج الرواتب الموحد
/// تم توحيده من جميع نماذج الرواتب في المشروع
class Payroll extends BaseModel {
  // معلومات أساسية
  final String payrollId;
  final String title;
  final String? description;
  final DateTime payPeriodStart;
  final DateTime payPeriodEnd;
  final DateTime paymentDate;

  // معلومات الحالة
  final PayrollStatus status;
  final bool isLocked;
  final String? approvedBy;
  final DateTime? approvedAt;

  // معلومات المبالغ
  final double totalBasicSalary;
  final double totalAllowances;
  final double totalDeductions;
  final double totalNetSalary;

  // معلومات الارتباط
  final String? departmentId;
  final String? departmentName;
  final String? branchId;
  final String? branchName;

  // معلومات إضافية
  final String? notes;
  final Map<String, dynamic>? metadata;
  final List<PayrollItem>? items;

  Payroll({
    String? id,
    required this.payrollId,
    required this.title,
    this.description,
    required this.payPeriodStart,
    required this.payPeriodEnd,
    required this.paymentDate,
    this.status = PayrollStatus.draft,
    this.isLocked = false,
    this.approvedBy,
    this.approvedAt,
    this.totalBasicSalary = 0.0,
    this.totalAllowances = 0.0,
    this.totalDeductions = 0.0,
    this.totalNetSalary = 0.0,
    this.departmentId,
    this.departmentName,
    this.branchId,
    this.branchName,
    this.notes,
    this.metadata,
    this.items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا الراتب مع استبدال الحقول المحددة بقيم جديدة
  Payroll copyWith({
    String? id,
    String? payrollId,
    String? title,
    String? description,
    DateTime? payPeriodStart,
    DateTime? payPeriodEnd,
    DateTime? paymentDate,
    PayrollStatus? status,
    bool? isLocked,
    String? approvedBy,
    DateTime? approvedAt,
    double? totalBasicSalary,
    double? totalAllowances,
    double? totalDeductions,
    double? totalNetSalary,
    String? departmentId,
    String? departmentName,
    String? branchId,
    String? branchName,
    String? notes,
    Map<String, dynamic>? metadata,
    List<PayrollItem>? items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Payroll(
      id: id ?? this.id,
      payrollId: payrollId ?? this.payrollId,
      title: title ?? this.title,
      description: description ?? this.description,
      payPeriodStart: payPeriodStart ?? this.payPeriodStart,
      payPeriodEnd: payPeriodEnd ?? this.payPeriodEnd,
      paymentDate: paymentDate ?? this.paymentDate,
      status: status ?? this.status,
      isLocked: isLocked ?? this.isLocked,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      totalBasicSalary: totalBasicSalary ?? this.totalBasicSalary,
      totalAllowances: totalAllowances ?? this.totalAllowances,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      totalNetSalary: totalNetSalary ?? this.totalNetSalary,
      departmentId: departmentId ?? this.departmentId,
      departmentName: departmentName ?? this.departmentName,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الراتب إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payroll_id': payrollId,
      'title': title,
      'description': description,
      'pay_period_start': payPeriodStart.toIso8601String(),
      'pay_period_end': payPeriodEnd.toIso8601String(),
      'payment_date': paymentDate.toIso8601String(),
      'status': status.index,
      'is_locked': isLocked ? 1 : 0,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'total_basic_salary': totalBasicSalary,
      'total_allowances': totalAllowances,
      'total_deductions': totalDeductions,
      'total_net_salary': totalNetSalary,
      'department_id': departmentId,
      'department_name': departmentName,
      'branch_id': branchId,
      'branch_name': branchName,
      'notes': notes,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'items': items?.map((item) => item.toMap()).toList(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء راتب من Map
  factory Payroll.fromMap(Map<String, dynamic> map) {
    return Payroll(
      id: map['id'],
      payrollId: map['payroll_id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'],
      payPeriodStart: map['pay_period_start'] != null
          ? DateTime.parse(map['pay_period_start'])
          : DateTime.now(),
      payPeriodEnd: map['pay_period_end'] != null
          ? DateTime.parse(map['pay_period_end'])
          : DateTime.now(),
      paymentDate: map['payment_date'] != null
          ? DateTime.parse(map['payment_date'])
          : DateTime.now(),
      status: PayrollStatus.values[map['status'] ?? 0],
      isLocked: map['is_locked'] == 1 || map['is_locked'] == true,
      approvedBy: map['approved_by'],
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'])
          : null,
      totalBasicSalary: map['total_basic_salary'] is int
          ? (map['total_basic_salary'] as int).toDouble()
          : (map['total_basic_salary'] as double? ?? 0.0),
      totalAllowances: map['total_allowances'] is int
          ? (map['total_allowances'] as int).toDouble()
          : (map['total_allowances'] as double? ?? 0.0),
      totalDeductions: map['total_deductions'] is int
          ? (map['total_deductions'] as int).toDouble()
          : (map['total_deductions'] as double? ?? 0.0),
      totalNetSalary: map['total_net_salary'] is int
          ? (map['total_net_salary'] as int).toDouble()
          : (map['total_net_salary'] as double? ?? 0.0),
      departmentId: map['department_id'],
      departmentName: map['department_name'],
      branchId: map['branch_id'],
      branchName: map['branch_name'],
      notes: map['notes'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      items: map['items'] != null
          ? List<PayrollItem>.from(
              (map['items'] as List).map((x) => PayrollItem.fromMap(x)))
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تحويل الراتب إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء راتب من JSON
  factory Payroll.fromJson(String source) =>
      Payroll.fromMap(jsonDecode(source));

  // Getters para compatibilidad con el código antiguo
  String get payrollNumber => payrollId;
  DateTime get periodStartDate => payPeriodStart;
  DateTime get periodEndDate => payPeriodEnd;
  List<PayrollDetail>? get details => items
      ?.map((item) => PayrollDetail(
              id: item.id,
              payrollId: id,
              employeeId: item.employeeId,
              employeeNumber: item.employeeCode ?? '',
              employeeName: item.employeeName,
              basicSalary: item.basicSalary,
              allowances: item.totalAllowances,
              deductions: item.totalDeductions,
              netSalary: item.netSalary,
              metadata: {
                'paymentMethod': item.paymentMethod,
                'paymentReference': item.paymentReference,
                'notes': item.notes,
              }))
      .toList();

  @override
  String toString() {
    return 'Payroll(id: $id, payrollId: $payrollId, title: $title, status: $status, totalNetSalary: $totalNetSalary)';
  }
}

/// نموذج عنصر الراتب
class PayrollItem {
  final String id;
  final String employeeId;
  final String employeeName;
  final String? employeeCode;
  final String? departmentId;
  final String? departmentName;
  final String? position;
  final double basicSalary;
  final Map<String, dynamic>? allowances;
  final Map<String, dynamic>? deductions;
  final double totalAllowances;
  final double totalDeductions;
  final double netSalary;
  final String? notes;
  final bool isPaid;
  final DateTime? paidAt;
  final String? paymentMethod;
  final String? paymentReference;

  PayrollItem({
    String? id,
    required this.employeeId,
    required this.employeeName,
    this.employeeCode,
    this.departmentId,
    this.departmentName,
    this.position,
    required this.basicSalary,
    this.allowances,
    this.deductions,
    required this.totalAllowances,
    required this.totalDeductions,
    required this.netSalary,
    this.notes,
    this.isPaid = false,
    this.paidAt,
    this.paymentMethod,
    this.paymentReference,
  }) : id = id ?? const Uuid().v4();

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  PayrollItem copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    String? employeeCode,
    String? departmentId,
    String? departmentName,
    String? position,
    double? basicSalary,
    Map<String, dynamic>? allowances,
    Map<String, dynamic>? deductions,
    double? totalAllowances,
    double? totalDeductions,
    double? netSalary,
    String? notes,
    bool? isPaid,
    DateTime? paidAt,
    String? paymentMethod,
    String? paymentReference,
  }) {
    return PayrollItem(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      employeeCode: employeeCode ?? this.employeeCode,
      departmentId: departmentId ?? this.departmentId,
      departmentName: departmentName ?? this.departmentName,
      position: position ?? this.position,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      deductions: deductions ?? this.deductions,
      totalAllowances: totalAllowances ?? this.totalAllowances,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      netSalary: netSalary ?? this.netSalary,
      notes: notes ?? this.notes,
      isPaid: isPaid ?? this.isPaid,
      paidAt: paidAt ?? this.paidAt,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
    );
  }

  /// تحويل عنصر الراتب إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'employee_name': employeeName,
      'employee_code': employeeCode,
      'department_id': departmentId,
      'department_name': departmentName,
      'position': position,
      'basic_salary': basicSalary,
      'allowances': allowances != null ? jsonEncode(allowances) : null,
      'deductions': deductions != null ? jsonEncode(deductions) : null,
      'total_allowances': totalAllowances,
      'total_deductions': totalDeductions,
      'net_salary': netSalary,
      'notes': notes,
      'is_paid': isPaid ? 1 : 0,
      'paid_at': paidAt?.toIso8601String(),
      'payment_method': paymentMethod,
      'payment_reference': paymentReference,
    };
  }

  /// إنشاء عنصر راتب من Map
  factory PayrollItem.fromMap(Map<String, dynamic> map) {
    return PayrollItem(
      id: map['id'] ?? const Uuid().v4(),
      employeeId: map['employee_id'] ?? '',
      employeeName: map['employee_name'] ?? '',
      employeeCode: map['employee_code'],
      departmentId: map['department_id'],
      departmentName: map['department_name'],
      position: map['position'],
      basicSalary: map['basic_salary'] is int
          ? (map['basic_salary'] as int).toDouble()
          : (map['basic_salary'] as double? ?? 0.0),
      allowances: map['allowances'] != null
          ? (map['allowances'] is String
              ? jsonDecode(map['allowances'])
              : map['allowances'] as Map<String, dynamic>)
          : null,
      deductions: map['deductions'] != null
          ? (map['deductions'] is String
              ? jsonDecode(map['deductions'])
              : map['deductions'] as Map<String, dynamic>)
          : null,
      totalAllowances: map['total_allowances'] is int
          ? (map['total_allowances'] as int).toDouble()
          : (map['total_allowances'] as double? ?? 0.0),
      totalDeductions: map['total_deductions'] is int
          ? (map['total_deductions'] as int).toDouble()
          : (map['total_deductions'] as double? ?? 0.0),
      netSalary: map['net_salary'] is int
          ? (map['net_salary'] as int).toDouble()
          : (map['net_salary'] as double? ?? 0.0),
      notes: map['notes'],
      isPaid: map['is_paid'] == 1 || map['is_paid'] == true,
      paidAt: map['paid_at'] != null ? DateTime.parse(map['paid_at']) : null,
      paymentMethod: map['payment_method'],
      paymentReference: map['payment_reference'],
    );
  }

  /// تحويل عنصر الراتب إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء عنصر راتب من JSON
  factory PayrollItem.fromJson(String source) =>
      PayrollItem.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'PayrollItem(id: $id, employeeName: $employeeName, netSalary: $netSalary)';
  }
}
