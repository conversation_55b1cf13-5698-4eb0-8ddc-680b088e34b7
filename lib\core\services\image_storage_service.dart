import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import '../providers/app_providers.dart';
import '../../features/settings/presenters/settings_presenter.dart';
import 'image_service.dart';

/// خدمة حفظ الصور بطرق مختلفة حسب إعدادات المستخدم
class ImageStorageService {
  static final ImageStorageService _instance = ImageStorageService._internal();
  factory ImageStorageService() => _instance;
  ImageStorageService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ImageService _imageService = ImageService();

  /// حفظ صورة المستخدم حسب الإعداد المحدد
  Future<String?> saveUserImage(
    File imageFile,
    String userId, {
    bool compressBeforeUpload = true,
    int quality = 85,
    int maxWidth = 800,
    int maxHeight = 800,
  }) async {
    try {
      // الحصول على إعداد حفظ الصور
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
        () => SettingsPresenter(),
      );
      final storageMode = settings.getImageStorageMode();

      AppLogger.info('🖼️ بدء حفظ صورة المستخدم بطريقة: $storageMode');

      switch (storageMode) {
        case 'local_only':
          return await _saveImageLocally(
            imageFile,
            userId,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        case 'database_only':
          return await _saveImageToDatabase(
            imageFile,
            userId,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        case 'both':
          return await _saveImageBoth(
            imageFile,
            userId,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        default:
          return await _saveImageLocally(
            imageFile,
            userId,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ صورة المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId, 'imagePath': imageFile.path},
      );
      return null;
    }
  }

  /// حفظ الصورة محلياً فقط
  Future<String?> _saveImageLocally(
    File imageFile,
    String userId, {
    required bool compressBeforeUpload,
    required int quality,
    required int maxWidth,
    required int maxHeight,
  }) async {
    try {
      AppLogger.info('💾 حفظ الصورة محلياً للمستخدم: $userId');

      final localPath = await _imageService.saveImageLocally(
        imageFile,
        'users/avatars',
        compressBeforeUpload: compressBeforeUpload,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (localPath != null) {
        AppLogger.info('✅ تم حفظ الصورة محلياً: $localPath');
      }

      return localPath;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة محلياً',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId},
      );
      return null;
    }
  }

  /// حفظ الصورة في قاعدة البيانات فقط
  Future<String?> _saveImageToDatabase(
    File imageFile,
    String userId, {
    required bool compressBeforeUpload,
    required int quality,
    required int maxWidth,
    required int maxHeight,
  }) async {
    try {
      AppLogger.info('🗄️ حفظ الصورة في قاعدة البيانات للمستخدم: $userId');

      // ضغط الصورة إذا لزم الأمر
      File fileToProcess = imageFile;
      if (compressBeforeUpload) {
        final compressedFile = await _imageService.compressAndConvertToWebP(
          imageFile,
          quality: quality,
          minWidth: maxWidth,
          minHeight: maxHeight,
        );
        if (compressedFile != null) {
          fileToProcess = compressedFile;
        }
      }

      // قراءة بيانات الصورة
      final Uint8List imageBytes = await fileToProcess.readAsBytes();

      // حفظ في قاعدة البيانات
      final db = await _databaseHelper.database;
      await db.update(
        'users',
        {
          'avatar_blob': imageBytes,
          'avatar': 'database_blob', // علامة تدل على أن الصورة في قاعدة البيانات
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      AppLogger.info('✅ تم حفظ الصورة في قاعدة البيانات');
      return 'database_blob';
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة في قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId},
      );
      return null;
    }
  }

  /// حفظ الصورة في الاثنين معاً
  Future<String?> _saveImageBoth(
    File imageFile,
    String userId, {
    required bool compressBeforeUpload,
    required int quality,
    required int maxWidth,
    required int maxHeight,
  }) async {
    try {
      AppLogger.info('🔄 حفظ الصورة في الاثنين معاً للمستخدم: $userId');

      // حفظ محلياً
      final localPath = await _saveImageLocally(
        imageFile,
        userId,
        compressBeforeUpload: compressBeforeUpload,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (localPath == null) {
        throw Exception('فشل في حفظ الصورة محلياً');
      }

      // حفظ في قاعدة البيانات أيضاً
      await _saveImageToDatabase(
        imageFile,
        userId,
        compressBeforeUpload: compressBeforeUpload,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      AppLogger.info('✅ تم حفظ الصورة في الاثنين معاً');
      return localPath; // نعيد المسار المحلي كمرجع أساسي
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة في الاثنين معاً',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId},
      );
      return null;
    }
  }

  /// استرجاع صورة المستخدم
  Future<dynamic> getUserImage(String userId, String? avatarPath) async {
    try {
      if (avatarPath == null || avatarPath.isEmpty) {
        return null;
      }

      // إذا كانت الصورة في قاعدة البيانات
      if (avatarPath == 'database_blob') {
        return await _getImageFromDatabase(userId);
      }

      // إذا كانت الصورة محلية
      if (await File(avatarPath).exists()) {
        return File(avatarPath);
      }

      // محاولة استرجاع من قاعدة البيانات كبديل
      return await _getImageFromDatabase(userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في استرجاع صورة المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId, 'avatarPath': avatarPath},
      );
      return null;
    }
  }

  /// استرجاع الصورة من قاعدة البيانات
  Future<Uint8List?> _getImageFromDatabase(String userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'users',
        columns: ['avatar_blob'],
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (result.isNotEmpty && result.first['avatar_blob'] != null) {
        return result.first['avatar_blob'] as Uint8List;
      }

      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في استرجاع الصورة من قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId},
      );
      return null;
    }
  }

  /// حذف صورة المستخدم
  Future<bool> deleteUserImage(String userId, String? avatarPath) async {
    try {
      AppLogger.info('🗑️ حذف صورة المستخدم: $userId');

      bool success = true;

      // حذف الملف المحلي إذا وجد
      if (avatarPath != null && 
          avatarPath.isNotEmpty && 
          avatarPath != 'database_blob') {
        final file = File(avatarPath);
        if (await file.exists()) {
          await file.delete();
          AppLogger.info('✅ تم حذف الملف المحلي');
        }
      }

      // حذف من قاعدة البيانات
      final db = await _databaseHelper.database;
      await db.update(
        'users',
        {
          'avatar': null,
          'avatar_blob': null,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      AppLogger.info('✅ تم حذف صورة المستخدم بنجاح');
      return success;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف صورة المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId, 'avatarPath': avatarPath},
      );
      return false;
    }
  }
}
