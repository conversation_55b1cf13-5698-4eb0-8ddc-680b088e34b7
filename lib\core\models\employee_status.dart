/// حالات الموظف
enum EmployeeStatus {
  /// نشط
  active,

  /// غير نشط
  inactive,

  /// في إجازة
  onLeave,

  /// منتهي الخدمة
  terminated,
}

/// الحصول على اسم حالة الموظف
String getEmployeeStatusName(EmployeeStatus status) {
  switch (status) {
    case EmployeeStatus.active:
      return 'نشط';
    case EmployeeStatus.inactive:
      return 'غير نشط';
    case EmployeeStatus.onLeave:
      return 'في إجازة';
    case EmployeeStatus.terminated:
      return 'منتهي الخدمة';
  }
}

/// الحصول على حالة الموظف من الاسم
EmployeeStatus getEmployeeStatusFromName(String name) {
  switch (name) {
    case 'نشط':
      return EmployeeStatus.active;
    case 'غير نشط':
      return EmployeeStatus.inactive;
    case 'في إجازة':
      return EmployeeStatus.onLeave;
    case 'منتهي الخدمة':
      return EmployeeStatus.terminated;
    default:
      return EmployeeStatus.active;
  }
}

/// الحصول على حالة الموظف من القيمة النصية
EmployeeStatus getEmployeeStatusFromString(String value) {
  switch (value) {
    case 'active':
      return EmployeeStatus.active;
    case 'inactive':
      return EmployeeStatus.inactive;
    case 'on_leave':
      return EmployeeStatus.onLeave;
    case 'terminated':
      return EmployeeStatus.terminated;
    default:
      return EmployeeStatus.active;
  }
}

/// تحويل حالة الموظف إلى نص
String employeeStatusToString(EmployeeStatus status) {
  switch (status) {
    case EmployeeStatus.active:
      return 'active';
    case EmployeeStatus.inactive:
      return 'inactive';
    case EmployeeStatus.onLeave:
      return 'on_leave';
    case EmployeeStatus.terminated:
      return 'terminated';
  }
}

/// تحويل نص إلى حالة موظف
EmployeeStatus stringToEmployeeStatus(String status) {
  switch (status.toLowerCase()) {
    case 'active':
      return EmployeeStatus.active;
    case 'inactive':
      return EmployeeStatus.inactive;
    case 'on_leave':
      return EmployeeStatus.onLeave;
    case 'terminated':
      return EmployeeStatus.terminated;
    default:
      return EmployeeStatus.active;
  }
}
