import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../presenters/product_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/product.dart';
import '../../../core/models/category.dart';
import '../widgets/product_form_dialog.dart';
import '../widgets/category_form_dialog.dart';

import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart'; // إضافة للتحميل الكسول

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  late final ProductPresenter _presenter;
  final _searchController = TextEditingController();
  bool _showSearchField = false; // متغير لإظهار/إخفاء حقل البحث

  // متغيرات طريقة العرض
  bool _isGridView = false; // false = قائمة، true = شبكة
  static const String _viewModeKey = 'products_view_mode';

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _presenter = AppProviders.getLazyPresenter<ProductPresenter>(
      () => ProductPresenter(),
    );
    _loadData();
    _loadViewPreference(); // تحميل تفضيل طريقة العرض

    // إضافة مستمع للبحث (نفس آلية UsersScreen)
    _searchController.addListener(() {
      _presenter.setSearchQuery(_searchController.text);
    });
  }

  // تحميل البيانات مع مراعاة النوع
  Future<void> _loadData() async {
    await Future.wait([
      _presenter.loadProducts(),
      _presenter.loadCategories(type: 'product'),
      _presenter.loadUnits(type: 'product'),
    ]);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل تفضيل طريقة العرض من SharedPreferences
  Future<void> _loadViewPreference() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isGridView = prefs.getBool(_viewModeKey) ?? false;
    });
  }

  /// حفظ تفضيل طريقة العرض في SharedPreferences
  Future<void> _saveViewPreference() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_viewModeKey, _isGridView);
  }

  /// تبديل طريقة العرض بين القائمة والشبكة
  void _toggleViewMode() {
    setState(() {
      _isGridView = !_isGridView;
    });
    _saveViewPreference();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'المنتجات',
        actions: [
          // زر البحث الذكي مع تكيف الثيمات
          AkAppBarSearchButton(
            isSearchActive: _showSearchField,
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          // زر الفلترة مع تصميم موحد
          AkAppBarButtons.filter(
            onPressed: _showFilterDialog,
          ),
          // زر تبديل العرض الذكي
          AkAppBarViewToggleButton(
            isGridView: _isGridView,
            onPressed: _toggleViewMode,
          ),
          // زر إدارة الفئات
          AkAppBarButtons.categories(
            onPressed: _showCategoriesDialog,
          ),
          // زر التقارير
          AkAppBarButtons.reports(
            onPressed: () {
              Navigator.pushNamed(context, '/reports');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث - نفس آلية UsersScreen)
          if (_showSearchField)
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.defaultMargin,
                vertical: AppDimensions.smallSpacing,
              ),
              child: AkSearchInput(
                controller: _searchController,
                hint: 'بحث في المنتجات (الاسم، الباركود، SKU، الفئة)...',
                onChanged: (value) {
                  // منطق البحث سيتم تطبيقه تلقائياً عبر المستمع في initState
                },
                onClear: () {
                  _searchController.clear();
                },
              ),
            ),
          Expanded(
            child: ListenableBuilder(
              listenable: _presenter,
              builder: (context, child) {
                if (_presenter.isLoading) {
                  return const AkLoadingIndicator(
                    message: 'جاري تحميل المنتجات...',
                  );
                }

                if (_presenter.error != null) {
                  return AkErrorState(
                    message: _presenter.error!,
                    onRetry: _presenter.loadProducts,
                  );
                }

                if (_presenter.products.isEmpty) {
                  return AkEmptyState(
                    icon: Icons.inventory_2_outlined,
                    title: 'لا توجد منتجات',
                    message: 'ابدأ بإضافة منتجاتك الأولى',
                    actionText: 'إضافة منتج',
                    onAction: _showAddProductDialog,
                  );
                }

                // عرض المنتجات حسب طريقة العرض المختارة
                return _isGridView ? _buildGridView() : _buildListView();
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AkFloatingButton(
        onPressed: _showAddProductDialog,
        icon: Icons.add,
        tooltip: 'إضافة منتج جديد',
      ),
    );
  }

  /// بناء العرض كقائمة عمودية
  Widget _buildListView() {
    return AkListView<Product>(
      items: _presenter.products,
      itemBuilder: (product, index) => _buildProductCard(product),
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.smallSpacing,
        vertical: AppDimensions.smallSpacing,
      ),
      showDividers: false,
      emptyMessage: 'لا توجد منتجات',
      emptyIcon: Icons.inventory_2_outlined,
      emptyActionText: 'إضافة منتج',
      onEmptyAction: _showAddProductDialog,
    );
  }

  /// بناء العرض كشبكة
  Widget _buildGridView() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.smallSpacing,
        vertical: AppDimensions.smallSpacing,
      ),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _getGridCrossAxisCount(),
          crossAxisSpacing: AppDimensions.smallSpacing,
          mainAxisSpacing: AppDimensions.smallSpacing,
          childAspectRatio: _getGridChildAspectRatio(),
        ),
        itemCount: _presenter.products.length,
        itemBuilder: (context, index) {
          return _buildGridProductCard(_presenter.products[index]);
        },
      ),
    );
  }

  /// تحديد عدد الأعمدة حسب حجم الشاشة
  int _getGridCrossAxisCount() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4; // شاشات كبيرة جداً
    if (screenWidth > 800) return 3; // شاشات كبيرة
    return 2; // شاشات صغيرة ومتوسطة
  }

  /// تحديد نسبة العرض إلى الارتفاع للبطاقات في الشبكة
  double _getGridChildAspectRatio() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 800) return 0.75; // شاشات كبيرة
    return 0.7; // شاشات صغيرة ومتوسطة
  }

  Widget _buildProductCard(Product product) {
    final isLowStock = product.isLowStock;
    final hasExpiry = product.hasExpiry;
    final categoryName = _presenter.getCategoryName(product.categoryId ?? '');
    final unitName = _presenter.getUnitName(product.unitId ?? '');

    // تحقق من وجود وحدات فرعية في البيانات الإضافية
    final hasSubUnits = product.metadata != null &&
        product.metadata!.containsKey('subUnits') &&
        product.metadata!['subUnits'] is List &&
        (product.metadata!['subUnits'] as List).isNotEmpty;

    return AkCard(
      margin: AppDimensions.cardPaddingMedium,
      onTap: () => _showProductDetailsDialog(product),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المنتج في صف منفصل مع تصميم مضغوط
              _buildProductImageHeader(product),

              const SizedBox(height: AppDimensions.spacing8),

              // تصميم متجاوب للمنتج مع إصلاح overflow
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المنتج مع تصميم متجاوب
                  Expanded(
                    child: _buildProductInfo(
                        product, categoryName, unitName, hasSubUnits),
                  ),

                  // معلومات المخزون مع تصميم مضغوط
                  _buildStockInfo(product, isLowStock, hasExpiry),
                ],
              ),

              // الوصف مع النظام الموحد
              if (product.description != null &&
                  product.description!.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacing8),
                Text(
                  product.description!,
                  style: AppTypography(
                    color: DynamicColors.textSecondary(context),
                    fontSize: AppDimensions.smallFontSize,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),

          // قائمة الإجراءات في أقصى اليمين وأقصى الأعلى
          Positioned(
            top: -17,
            right: -21,
            child: _buildProductActionsMenu(product),
          ),
        ],
      ),
    );
  }

////////////////////////////////////////////////
  /// بناء رأس صورة المنتج مع تصميم مضغوط وجذاب
  Widget _buildProductImageHeader(Product product) {
    final hasImage = product.imageUrl != null && product.imageUrl!.isNotEmpty;

    return Container(
      height: 120, // ارتفاع مضغوط للصورة
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        border: Border.all(
          color: DynamicColors.border(context),
          width: 1,
        ),
        gradient: hasImage
            ? null
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  DynamicColors.surfaceVariant(context),
                  DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
                ],
              ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        child: hasImage
            ? Image.file(
                File(product.imageUrl!),
                fit: BoxFit.cover,
                width: double.infinity,
                height: 120,
                errorBuilder: (context, error, stackTrace) =>
                    _buildImagePlaceholder(Icons.broken_image_outlined),
              )
            : _buildImagePlaceholder(Icons.image_not_supported_outlined),
      ),
    );
  }

  /// بناء عنصر نائب للصورة مع تصميم محسن
  Widget _buildImagePlaceholder(IconData icon) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DynamicColors.surfaceVariant(context),
            DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppDimensions.iconSizeLarge,
            color: DynamicColors.onSurfaceVariant(context),
          ),
          const SizedBox(height: AppDimensions.spacing4),
          Text(
            'لا توجد صورة',
            style: AppTypography(
              color: DynamicColors.onSurfaceVariant(context),
              fontSize: AppDimensions.smallFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات المنتج مع النظام الموحد
  Widget _buildProductInfo(
      Product product, String categoryName, String unitName, bool hasSubUnits) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اسم المنتج مع النظام الموحد
        Text(
          product.name,
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // معلومات إضافية مع تصميم متجاوب
        const SizedBox(height: AppDimensions.spacing4),
        _buildCategoryAndUnit(categoryName, unitName),

        // الباركود أو SKU مع تصميم متجاوب
        if (product.barcode != null || product.sku != null) ...[
          const SizedBox(height: AppDimensions.spacing4),
          _buildBarcodeInfo(product),
        ],

        // السعر مع تصميم متجاوب
        const SizedBox(height: AppDimensions.spacing4),
        _buildPriceInfo(product, hasSubUnits),
      ],
    );
  }

  /// بناء معلومات الفئة والوحدة
  Widget _buildCategoryAndUnit(String categoryName, String unitName) {
    return Wrap(
      spacing: AppDimensions.spacing8,
      runSpacing: AppDimensions.spacing4,
      children: [
        _buildInfoChip(Icons.category_outlined, categoryName),
        _buildInfoChip(Icons.straighten, unitName),
      ],
    );
  }

  /// بناء رقاقة معلومات صغيرة
  Widget _buildInfoChip(IconData icon, String text) {
    // تطبيق ألوان ديناميكية مختلفة حسب نوع الأيقونة
    final iconColor = icon == Icons.category_outlined
        ? DynamicColors.primary
        : DynamicColors.textSecondary(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: AppDimensions.iconSizeSmall,
          color: iconColor,
        ),
        const SizedBox(width: AppDimensions.spacing4),
        Flexible(
          child: Text(
            text,
            style: AppTypography(
              color: DynamicColors.textSecondary(context),
              fontSize: AppDimensions.smallFontSize,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الباركود و SKU
  Widget _buildBarcodeInfo(Product product) {
    return Wrap(
      spacing: AppDimensions.spacing8,
      runSpacing: AppDimensions.spacing4,
      children: [
        if (product.barcode != null)
          _buildBarcodeChip(Icons.qr_code, product.barcode!),
        if (product.sku != null)
          _buildBarcodeChip(Icons.inventory_2_outlined, 'SKU: ${product.sku}'),
      ],
    );
  }

  /// بناء رقاقة معلومات الباركود/SKU مع ألوان ديناميكية
  Widget _buildBarcodeChip(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: AppDimensions.iconSizeSmall,
          color: DynamicColors.textSecondary(context),
        ),
        const SizedBox(width: AppDimensions.spacing4),
        Flexible(
          child: Text(
            text,
            style: AppTypography(
              color: DynamicColors.textSecondary(context),
              fontSize: AppDimensions.smallFontSize,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// بناء معلومات السعر
  Widget _buildPriceInfo(Product product, bool hasSubUnits) {
    return Wrap(
      spacing: AppDimensions.spacing8,
      runSpacing: AppDimensions.spacing4,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        Text(
          '${product.salePrice} ر.س',
          style: AppTypography(
            color: DynamicColors.primary,
            fontWeight: FontWeight.bold,
            fontSize: AppDimensions.mediumFontSize,
          ),
        ),
        if (hasSubUnits)
          Container(
            padding: AppDimensions.getCardPadding('tiny'),
            decoration: BoxDecoration(
              color: DynamicColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
            child: Text(
              '${(product.metadata?['subUnits'] as List?)?.length ?? 0} وحدة فرعية',
              style: AppTypography(
                color: DynamicColors.info,
                fontSize: AppDimensions.tinyFontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  /// بناء معلومات المخزون مع تصميم مضغوط
  Widget _buildStockInfo(Product product, bool isLowStock, bool hasExpiry) {
    return SizedBox(
      width: 60, // عرض ثابت لمنع overflow
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // رقم المخزون فقط
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.spacing4,
              vertical: AppDimensions.spacing2,
            ),
            decoration: BoxDecoration(
              color: isLowStock
                  ? DynamicColors.error.withValues(alpha: 0.1)
                  : DynamicColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '${product.quantity}',
              style: AppTypography(
                color: isLowStock ? DynamicColors.error : DynamicColors.success,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // أيقونة تحذير إذا كان المخزون منخفض
          if (isLowStock) ...[
            const SizedBox(height: 2),
            Icon(
              Icons.warning_amber_rounded,
              size: 12,
              color: DynamicColors.warning,
            ),
          ],

          // أيقونة انتهاء الصلاحية
          if (hasExpiry && product.expiryDate != null) ...[
            const SizedBox(height: 2),
            Icon(
              Icons.event,
              size: 12,
              color: DynamicColors.warning,
            ),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة منتج مخصصة للعرض الشبكي
  Widget _buildGridProductCard(Product product) {
    final isLowStock = product.isLowStock;
    final categoryName = _presenter.getCategoryName(product.categoryId ?? '');
///////////////بطاقة العرض الشبكي ////////////////////

    return AkCard(
      margin: AppDimensions.cardPaddingTiny,
      onTap: () => _showProductDetailsDialog(product),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المنتج مع ارتفاع أصغر للشبكة
              _buildGridProductImage(product),

              const SizedBox(height: AppDimensions.spacing8),

              // معلومات المنتج مضغوطة للشبكة
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.spacing4), // هامش داخلي صغير
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المنتج
                      Text(
                        product.name,
                        style: AppTypography(
                          fontSize: AppDimensions.smallFontSize,
                          fontWeight: FontWeight.bold,
                          color: DynamicColors.textPrimary(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppDimensions.spacing4),

                      // الفئة مضغوطة
                      Row(
                        children: [
                          Icon(
                            Icons.category_outlined,
                            size: 10,
                            color: DynamicColors.primary,
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: Text(
                              categoryName,
                              style: AppTypography(
                                color: DynamicColors.textSecondary(context),
                                fontSize: AppDimensions.tinyFontSize,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      // الباركود أو SKU مضغوط
                      if (product.barcode != null || product.sku != null) ...[
                        const SizedBox(height: AppDimensions.spacing2),
                        Row(
                          children: [
                            Icon(
                              product.barcode != null
                                  ? Icons.qr_code
                                  : Icons.inventory_2_outlined,
                              size: 10,
                              color: DynamicColors.textSecondary(context),
                            ),
                            const SizedBox(width: 2),
                            Expanded(
                              child: Text(
                                product.barcode ?? product.sku ?? '',
                                style: AppTypography(
                                  color: DynamicColors.textSecondary(context),
                                  fontSize: 8,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],

                      const Spacer(),

                      // السعر والمخزون في الأسفل
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // السعر
                          Text(
                            '${product.salePrice} ر.س',
                            style: AppTypography(
                              color: DynamicColors.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: AppDimensions.smallFontSize,
                            ),
                          ),
                          // المخزون
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.spacing8,
                              vertical: AppDimensions.spacing2,
                            ),
                            decoration: BoxDecoration(
                              color: isLowStock
                                  ? DynamicColors.error.withValues(alpha: 0.1)
                                  : DynamicColors.success
                                      .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${product.quantity}',
                              style: AppTypography(
                                color: isLowStock
                                    ? DynamicColors.error
                                    : DynamicColors.success,
                                fontSize: AppDimensions.tinyFontSize,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // قائمة الإجراءات في الزاوية العلوية اليمنى للشبكة
          Positioned(
            top: -19,
            right: -21,
            child: _buildProductActionsMenu(product),
          ),
        ],
      ),
    );
  }

/////////////////////////////////////////////////
  /// بناء صورة المنتج للعرض الشبكي
  Widget _buildGridProductImage(Product product) {
    final hasImage = product.imageUrl != null && product.imageUrl!.isNotEmpty;

    return Container(
      height: 100, // ارتفاع أصغر للشبكة
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        border: Border.all(
          color: DynamicColors.border(context),
          width: 1,
        ),
        gradient: hasImage
            ? null
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  DynamicColors.surfaceVariant(context),
                  DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
                ],
              ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        child: hasImage
            ? Image.file(
                File(product.imageUrl!),
                fit: BoxFit.cover,
                width: double.infinity,
                height: 100,
                errorBuilder: (context, error, stackTrace) =>
                    _buildGridImagePlaceholder(Icons.broken_image_outlined),
              )
            : _buildGridImagePlaceholder(Icons.image_not_supported_outlined),
      ),
    );
  }

  /// بناء عنصر نائب للصورة في الشبكة
  Widget _buildGridImagePlaceholder(IconData icon) {
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DynamicColors.surfaceVariant(context),
            DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppDimensions.iconSizeMedium,
            color: DynamicColors.onSurfaceVariant(context),
          ),
          const SizedBox(height: 2),
          Text(
            'لا توجد صورة',
            style: AppTypography(
              color: DynamicColors.onSurfaceVariant(context),
              fontSize: AppDimensions.tinyFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الإجراءات للمنتج
  Widget _buildProductActionsMenu(Product product) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: DynamicColors.textSecondary(context),
        size: 16, // حجم صغير ثابت
      ),
      padding: EdgeInsets.zero, // إزالة الهوامش
      constraints: const BoxConstraints(
        minWidth: 24, // عرض أدنى صغير
        minHeight: 24, // ارتفاع أدنى صغير
      ),
      offset: const Offset(-8, 0), // إزاحة القائمة قليلاً لليسار
      onSelected: (value) => _handleProductAction(value, product),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'details',
          child: Row(
            children: [
              Icon(Icons.info_outline, size: AppDimensions.iconSizeSmall),
              SizedBox(width: AppDimensions.spacing8),
              Text('عرض التفاصيل'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit_outlined, size: AppDimensions.iconSizeSmall),
              SizedBox(width: AppDimensions.spacing8),
              Text('تعديل'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete_outline,
                  size: AppDimensions.iconSizeSmall,
                  color: DynamicColors.error),
              const SizedBox(width: AppDimensions.spacing8),
              Text('حذف', style: TextStyle(color: DynamicColors.error)),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'buy',
          child: Row(
            children: [
              Icon(Icons.shopping_cart_outlined,
                  size: AppDimensions.iconSizeSmall),
              SizedBox(width: AppDimensions.spacing8),
              Text('شراء'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'sell',
          child: Row(
            children: [
              Icon(Icons.point_of_sale_outlined,
                  size: AppDimensions.iconSizeSmall),
              SizedBox(width: AppDimensions.spacing8),
              Text('بيع'),
            ],
          ),
        ),
      ],
    );
  }

  /// معالجة إجراءات المنتج
  void _handleProductAction(String action, Product product) {
    switch (action) {
      case 'details':
        _showProductDetailsDialog(product);
        break;
      case 'edit':
        _showEditProductDialog(product);
        break;
      case 'delete':
        _showDeleteConfirmation(product);
        break;
      case 'buy':
        _handleBuyProduct(product);
        break;
      case 'sell':
        _handleSellProduct(product);
        break;
    }
  }

  /// معالجة شراء المنتج
  void _handleBuyProduct(Product product) {
    // TODO: تطبيق منطق الشراء
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم تطبيق وظيفة الشراء لاحقاً للمنتج: ${product.name}'),
        backgroundColor: DynamicColors.info,
      ),
    );
  }

  /// معالجة بيع المنتج
  void _handleSellProduct(Product product) {
    // TODO: تطبيق منطق البيع
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم تطبيق وظيفة البيع لاحقاً للمنتج: ${product.name}'),
        backgroundColor: DynamicColors.success,
      ),
    );
  }

  void _showFilterDialog() {
    final categoryPresenter = AppProviders.getCategoryPresenter();
    categoryPresenter.loadCategories(type: 'product');

    AkDialogs.info(
      context: context,
      title: 'تصفية المنتجات',
      message: 'اختر الفلاتر المطلوبة',
    );
  }

  void _showAddProductDialog() {
    showDialog(
      context: context,
      builder: (context) => const ProductFormDialog(),
    ).then((product) {
      if (product != null) {
        _presenter.addProduct(product);
      }
    });
  }

  /// عرض نافذة تفاصيل المنتج
  void _showProductDetailsDialog(Product product) {
    showDialog(
      context: context,
      builder: (context) => _buildProductDetailsDialog(product),
    );
  }

  /// بناء نافذة تفاصيل المنتج
  Widget _buildProductDetailsDialog(Product product) {
    final categoryName = _presenter.getCategoryName(product.categoryId ?? '');
    final unitName = _presenter.getUnitName(product.unitId ?? '');
    final hasSubUnits = product.metadata != null &&
        product.metadata!.containsKey('subUnits') &&
        product.metadata!['subUnits'] is List &&
        (product.metadata!['subUnits'] as List).isNotEmpty;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            _buildDetailsHeader(product),

            // محتوى النافذة
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(AppDimensions.defaultMargin),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صورة المنتج
                    _buildDetailsImage(product),

                    const SizedBox(height: AppDimensions.spacing16),

                    // معلومات أساسية
                    _buildDetailsBasicInfo(product, categoryName, unitName),

                    const SizedBox(height: AppDimensions.spacing16),

                    // معلومات المخزون والأسعار
                    _buildDetailsStockInfo(product),

                    if (product.description != null &&
                        product.description!.isNotEmpty) ...[
                      const SizedBox(height: AppDimensions.spacing16),
                      _buildDetailsDescription(product),
                    ],

                    if (hasSubUnits) ...[
                      const SizedBox(height: AppDimensions.spacing16),
                      _buildDetailsSubUnits(product),
                    ],

                    if (product.expiryDate != null) ...[
                      const SizedBox(height: AppDimensions.spacing16),
                      _buildDetailsExpiryInfo(product),
                    ],
                  ],
                ),
              ),
            ),

            // أزرار الإجراءات
            _buildDetailsActions(product),
          ],
        ),
      ),
    );
  }

  /// بناء رأس نافذة التفاصيل
  Widget _buildDetailsHeader(Product product) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.defaultMargin),
      decoration: BoxDecoration(
        color: DynamicColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusMedium),
          topRight: Radius.circular(AppDimensions.radiusMedium),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              product.name,
              style: AppTypography(
                fontSize: AppDimensions.largeFontSize,
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'إغلاق',
          ),
        ],
      ),
    );
  }

  /// بناء صورة المنتج في نافذة التفاصيل
  Widget _buildDetailsImage(Product product) {
    final hasImage = product.imageUrl != null && product.imageUrl!.isNotEmpty;

    return Center(
      child: Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          border: Border.all(
            color: DynamicColors.border(context),
            width: 1,
          ),
          gradient: hasImage
              ? null
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    DynamicColors.surfaceVariant(context),
                    DynamicColors.surfaceVariant(context)
                        .withValues(alpha: 0.7),
                  ],
                ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          child: hasImage
              ? Image.file(
                  File(product.imageUrl!),
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 200,
                  errorBuilder: (context, error, stackTrace) =>
                      _buildDetailsImagePlaceholder(
                          Icons.broken_image_outlined),
                )
              : _buildDetailsImagePlaceholder(
                  Icons.image_not_supported_outlined),
        ),
      ),
    );
  }

  /// بناء عنصر نائب للصورة في نافذة التفاصيل
  Widget _buildDetailsImagePlaceholder(IconData icon) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DynamicColors.surfaceVariant(context),
            DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppDimensions.iconSizeXLarge,
            color: DynamicColors.onSurfaceVariant(context),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            'لا توجد صورة',
            style: AppTypography(
              color: DynamicColors.onSurfaceVariant(context),
              fontSize: AppDimensions.mediumFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء المعلومات الأساسية في نافذة التفاصيل
  Widget _buildDetailsBasicInfo(
      Product product, String categoryName, String unitName) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        _buildDetailRow('الفئة', categoryName, Icons.category_outlined),
        _buildDetailRow('الوحدة', unitName, Icons.straighten),
        if (product.barcode != null)
          _buildDetailRow('الباركود', product.barcode!, Icons.qr_code),
        if (product.sku != null)
          _buildDetailRow('SKU', product.sku!, Icons.inventory_2_outlined),
      ],
    );
  }

  /// بناء معلومات المخزون والأسعار
  Widget _buildDetailsStockInfo(Product product) {
    final isLowStock = product.isLowStock;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المخزون والأسعار',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Row(
          children: [
            Expanded(
              child: _buildStockCard('الكمية المتاحة', '${product.quantity}',
                  isLowStock ? DynamicColors.error : DynamicColors.success),
            ),
            const SizedBox(width: AppDimensions.spacing8),
            Expanded(
              child: _buildStockCard(
                  'الحد الأدنى', '${product.minStock}', DynamicColors.warning),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Row(
          children: [
            Expanded(
              child: _buildStockCard('سعر الشراء',
                  '${product.purchasePrice} ر.س', DynamicColors.info),
            ),
            const SizedBox(width: AppDimensions.spacing8),
            Expanded(
              child: _buildStockCard('سعر البيع', '${product.salePrice} ر.س',
                  DynamicColors.success),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة معلومات المخزون
  Widget _buildStockCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.spacing12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              color: DynamicColors.textSecondary(context),
            ),
          ),
          const SizedBox(height: AppDimensions.spacing4),
          Text(
            value,
            style: AppTypography(
              fontSize: AppDimensions.mediumFontSize,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.spacing4),
      child: Row(
        children: [
          Icon(
            icon,
            size: AppDimensions.iconSizeSmall,
            color: DynamicColors.textSecondary(context),
          ),
          const SizedBox(width: AppDimensions.spacing8),
          Text(
            '$label: ',
            style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              color: DynamicColors.textSecondary(context),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography(
                fontSize: AppDimensions.smallFontSize,
                color: DynamicColors.textPrimary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء وصف المنتج
  Widget _buildDetailsDescription(Product product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوصف',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.spacing12),
          decoration: BoxDecoration(
            color: DynamicColors.surfaceVariant(context),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
          child: Text(
            product.description ?? '',
            style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              color: DynamicColors.textPrimary(context),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الوحدات الفرعية
  Widget _buildDetailsSubUnits(Product product) {
    final subUnits = product.metadata?['subUnits'] as List? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوحدات الفرعية',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        ...subUnits
            .map((subUnit) => Container(
                  margin: const EdgeInsets.only(bottom: AppDimensions.spacing4),
                  padding: const EdgeInsets.all(AppDimensions.spacing8),
                  decoration: BoxDecoration(
                    color: DynamicColors.surfaceVariant(context),
                    borderRadius:
                        BorderRadius.circular(AppDimensions.radiusSmall),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.subdirectory_arrow_right,
                        size: AppDimensions.iconSizeSmall,
                        color: DynamicColors.textSecondary(context),
                      ),
                      const SizedBox(width: AppDimensions.spacing8),
                      Expanded(
                        child: Text(
                          '${subUnit['name']} (${subUnit['quantity']} ${subUnit['unit']})',
                          style: AppTypography(
                            fontSize: AppDimensions.smallFontSize,
                            color: DynamicColors.textPrimary(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }

  /// بناء معلومات انتهاء الصلاحية
  Widget _buildDetailsExpiryInfo(Product product) {
    final expiryDate = product.expiryDate;
    final isExpired = expiryDate != null && expiryDate.isBefore(DateTime.now());
    final isNearExpiry = expiryDate != null &&
        expiryDate.difference(DateTime.now()).inDays <= 30 &&
        !isExpired;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الصلاحية',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.spacing12),
          decoration: BoxDecoration(
            color: isExpired
                ? DynamicColors.error.withValues(alpha: 0.1)
                : isNearExpiry
                    ? DynamicColors.warning.withValues(alpha: 0.1)
                    : DynamicColors.success.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            border: Border.all(
              color: isExpired
                  ? DynamicColors.error
                  : isNearExpiry
                      ? DynamicColors.warning
                      : DynamicColors.success,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isExpired
                    ? Icons.error_outline
                    : isNearExpiry
                        ? Icons.warning_amber_outlined
                        : Icons.check_circle_outline,
                color: isExpired
                    ? DynamicColors.error
                    : isNearExpiry
                        ? DynamicColors.warning
                        : DynamicColors.success,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ انتهاء الصلاحية',
                      style: AppTypography(
                        fontSize: AppDimensions.smallFontSize,
                        color: DynamicColors.textSecondary(context),
                      ),
                    ),
                    Text(
                      '${expiryDate?.day}/${expiryDate?.month}/${expiryDate?.year}',
                      style: AppTypography(
                        fontSize: AppDimensions.mediumFontSize,
                        fontWeight: FontWeight.bold,
                        color: isExpired
                            ? DynamicColors.error
                            : isNearExpiry
                                ? DynamicColors.warning
                                : DynamicColors.success,
                      ),
                    ),
                    if (isExpired)
                      Text(
                        'منتهي الصلاحية',
                        style: AppTypography(
                          fontSize: AppDimensions.smallFontSize,
                          color: DynamicColors.error,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    else if (isNearExpiry)
                      Text(
                        'قريب من انتهاء الصلاحية',
                        style: AppTypography(
                          fontSize: AppDimensions.smallFontSize,
                          color: DynamicColors.warning,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات في نافذة التفاصيل
  Widget _buildDetailsActions(Product product) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.defaultMargin),
      decoration: BoxDecoration(
        color: DynamicColors.surfaceVariant(context),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusMedium),
          bottomRight: Radius.circular(AppDimensions.radiusMedium),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: AkButton(
              text: 'تعديل',
              onPressed: () {
                Navigator.of(context).pop();
                _showEditProductDialog(product);
              },
              icon: Icons.edit_outlined,
            ),
          ),
          const SizedBox(width: AppDimensions.spacing8),
          Expanded(
            child: AkButton(
              text: 'حذف',
              onPressed: () {
                Navigator.of(context).pop();
                _showDeleteConfirmation(product);
              },
              icon: Icons.delete_outline,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد حذف المنتج
  void _showDeleteConfirmation(Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المنتج "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _presenter.deleteProduct(product.id);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showEditProductDialog(Product product) {
    showDialog(
      context: context,
      builder: (context) => ProductFormDialog(product: product),
    ).then((updatedProduct) {
      if (updatedProduct != null) {
        _presenter.updateProduct(updatedProduct);
      }
    });
  }

  void _showCategoriesDialog() {
    final categoryPresenter = AppProviders.getCategoryPresenter();
    categoryPresenter.loadCategories(type: 'product');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة التصنيفات'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListenableBuilder(
            listenable: categoryPresenter,
            builder: (context, child) {
              if (categoryPresenter.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (categoryPresenter.categories.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.category_outlined,
                        size: 48,
                        color: AppColors.lightTextSecondary,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد تصنيفات',
                        style: AppTypography(
                          fontSize: 16,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.separated(
                shrinkWrap: true,
                itemCount: categoryPresenter.categories.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final category = categoryPresenter.categories[index];
                  return AkCard(
                    onTap: () => _showEditCategoryDialog(category),
                    child: ListTile(
                      leading: const Icon(Icons.category_outlined),
                      title: Text(category.name),
                      subtitle: category.description != null
                          ? Text(
                              category.description!,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            )
                          : null,
                      trailing: AkIconButton(
                        icon: Icons.edit,
                        size: AkButtonSize.small,
                        tooltip: 'تعديل التصنيف',
                        onPressed: () => _showEditCategoryDialog(category),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _showAddCategoryDialog();
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة تصنيف'),
          ),
        ],
      ),
    );
  }

  void _showAddCategoryDialog() {
    showDialog(
      context: context,
      builder: (context) => const CategoryFormDialog(),
    ).then((category) {
      if (category != null && mounted) {
        final categoryPresenter = AppProviders.getCategoryPresenter();
        categoryPresenter.addCategory(category);
      }
    });
  }

  void _showEditCategoryDialog(Category category) {
    showDialog(
      context: context,
      builder: (context) => CategoryFormDialog(category: category),
    ).then((updatedCategory) {
      if (updatedCategory != null && mounted) {
        final categoryPresenter = AppProviders.getCategoryPresenter();
        categoryPresenter.updateCategory(updatedCategory);
      }
    });
  }
}
