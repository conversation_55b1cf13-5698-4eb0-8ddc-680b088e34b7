import 'package:flutter/material.dart';

import 'package:tajer_plus/core/models/employee.dart';
import 'package:tajer_plus/core/models/employee_status.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

/// شاشة موحدة لإدارة الموظفين (عرض، إضافة، تعديل، حذف، طباعة)
/// تم تصميمها لتكون أكثر كفاءة وسرعة في الاستخدام
class UnifiedEmployeesScreen extends StatefulWidget {
  const UnifiedEmployeesScreen({Key? key}) : super(key: key);

  @override
  State<UnifiedEmployeesScreen> createState() => _UnifiedEmployeesScreenState();
}

class _UnifiedEmployeesScreenState extends State<UnifiedEmployeesScreen>
    with SingleTickerProviderStateMixin {
  // حالة الشاشة
  bool _isLoading = true;
  bool _isFormVisible = false;
  bool _isFormSubmitting = false;
  bool _isFilterExpanded = false;
  Employee? _selectedEmployee;

  // وحدات التحكم للبحث والتصفية
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  String _statusFilter = 'الكل';
  String _departmentFilter = 'الكل';

  // وحدات التحكم لنموذج الموظف
  final _formKey = GlobalKey<FormState>();
  final _employeeIdController = TextEditingController(); // رقم الموظف
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _positionController = TextEditingController();
  final _departmentController = TextEditingController();
  final _salaryController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _bankAccountController = TextEditingController();
  final _bankNameController = TextEditingController();

  DateTime _hireDate = DateTime.now();
  DateTime? _birthDate;
  String _status = 'active';
  String? _gender;

  // قوائم البيانات
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  final List<String> _departments = [
    'الكل',
    'المبيعات',
    'المالية',
    'الموارد البشرية',
    'المستودعات',
    'تكنولوجيا المعلومات'
  ];
  final List<String> _statusOptions = [
    'الكل',
    'نشط',
    'غير نشط',
    'في إجازة',
    'منتهي الخدمة'
  ];
  final List<String> _genderOptions = ['ذكر', 'أنثى'];

  // متحكم التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadEmployees();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _employeeIdController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    _salaryController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    _nationalIdController.dispose();
    _bankAccountController.dispose();
    _bankNameController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الموظفين
  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة جلب بيانات الموظفين
      // مثال:
      // final employeeService = EmployeeService();
      // final employees = await employeeService.getEmployees();

      // بيانات تجريبية للعرض
      await Future.delayed(const Duration(seconds: 1));
      final dummyEmployees = [
        Employee(
          id: '1',
          employeeId: 'EMP001',
          firstName: 'أحمد',
          lastName: 'محمد',
          fullName: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '**********',
          position: 'مدير مبيعات',
          department: 'المبيعات',
          basicSalary: 5000,
          hireDate: DateTime.now().subtract(const Duration(days: 365)),
          status: employeeStatusToString(EmployeeStatus.active),
        ),
        Employee(
          id: '2',
          employeeId: 'EMP002',
          firstName: 'سارة',
          lastName: 'أحمد',
          fullName: 'سارة أحمد',
          email: '<EMAIL>',
          phone: '0123456788',
          position: 'محاسب',
          department: 'المالية',
          basicSalary: 4500,
          hireDate: DateTime.now().subtract(const Duration(days: 180)),
          status: employeeStatusToString(EmployeeStatus.active),
        ),
        Employee(
          id: '3',
          employeeId: 'EMP003',
          firstName: 'محمد',
          lastName: 'علي',
          fullName: 'محمد علي',
          email: '<EMAIL>',
          phone: '0123456787',
          position: 'مندوب مبيعات',
          department: 'المبيعات',
          basicSalary: 3500,
          hireDate: DateTime.now().subtract(const Duration(days: 90)),
          status: employeeStatusToString(EmployeeStatus.onLeave),
        ),
      ];

      setState(() {
        _employees = dummyEmployees;
        _filteredEmployees = dummyEmployees;
        _isLoading = false;
      });
    } catch (e) {
      // معالجة الأخطاء
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل بيانات الموظفين');
    }
  }

  /// البحث في قائمة الموظفين
  void _searchEmployees(String query) {
    _applyFilters(query, _statusFilter, _departmentFilter);
  }

  /// تطبيق التصفية على قائمة الموظفين
  void _applyFilters(
      String query, String statusFilter, String departmentFilter) {
    List<Employee> filtered = _employees;

    // تطبيق تصفية البحث
    if (query.isNotEmpty) {
      final searchLower = query.toLowerCase();
      filtered = filtered.where((employee) {
        final name = employee.fullName.toLowerCase();
        final number = employee.employeeNumber.toLowerCase();
        final position = employee.positionName?.toLowerCase() ?? '';
        final department = employee.departmentName?.toLowerCase() ?? '';

        return name.contains(searchLower) ||
            number.contains(searchLower) ||
            position.contains(searchLower) ||
            department.contains(searchLower);
      }).toList();
    }

    // تطبيق تصفية الحالة
    if (statusFilter != 'الكل') {
      String? statusStr;
      switch (statusFilter) {
        case 'نشط':
          statusStr = 'active';
          break;
        case 'غير نشط':
          statusStr = 'inactive';
          break;
        case 'في إجازة':
          statusStr = 'on_leave';
          break;
        case 'منتهي الخدمة':
          statusStr = 'terminated';
          break;
      }

      if (statusStr != null) {
        filtered =
            filtered.where((employee) => employee.status == statusStr).toList();
      }
    }

    // تطبيق تصفية القسم
    if (departmentFilter != 'الكل') {
      filtered = filtered
          .where((employee) => employee.departmentName == departmentFilter)
          .toList();
    }

    setState(() {
      _filteredEmployees = filtered;
    });
  }

  /// توليد رقم موظف جديد
  void _generateNewEmployeeNumber() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final random = (1000 + now.millisecond).toString().substring(1);
    _employeeIdController.text = 'EMP$year$month$random';
  }

  /// إعداد نموذج إضافة موظف جديد
  void _setupAddEmployeeForm() {
    _clearForm();
    _generateNewEmployeeNumber();
    setState(() {
      _selectedEmployee = null;
      _isFormVisible = true;
    });
  }

  /// إعداد نموذج تعديل موظف
  void _setupEditEmployeeForm(Employee employee) {
    _clearForm();
    _employeeIdController.text = employee.employeeId;
    _firstNameController.text = employee.firstName ?? '';
    _lastNameController.text = employee.lastName ?? '';
    _emailController.text = employee.email ?? '';
    _phoneController.text = employee.phone ?? '';
    _positionController.text = employee.position ?? '';
    _departmentController.text = employee.department ?? '';
    _salaryController.text = employee.basicSalary.toString();
    _addressController.text = employee.address ?? '';
    _notesController.text = '';
    _nationalIdController.text = employee.nationalId ?? '';
    _bankAccountController.text = employee.bankAccount ?? '';
    _bankNameController.text = employee.bankName ?? '';
    _hireDate = employee.hireDate ?? DateTime.now();
    _birthDate = null;
    _status = employee.status;
    _gender = employee.gender;

    setState(() {
      _selectedEmployee = employee;
      _isFormVisible = true;
    });
  }

  /// مسح النموذج
  void _clearForm() {
    _formKey.currentState?.reset();
    _employeeIdController.clear();
    _firstNameController.clear();
    _lastNameController.clear();
    _emailController.clear();
    _phoneController.clear();
    _positionController.clear();
    _departmentController.clear();
    _salaryController.clear();
    _addressController.clear();
    _notesController.clear();
    _nationalIdController.clear();
    _bankAccountController.clear();
    _bankNameController.clear();
    _hireDate = DateTime.now();
    _birthDate = null;
    _status = 'active';
    _gender = null;
  }

  /// إغلاق النموذج
  void _closeForm() {
    setState(() {
      _isFormVisible = false;
      _selectedEmployee = null;
    });
  }

  /// حفظ بيانات الموظف
  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isFormSubmitting = true;
    });

    try {
      // تجهيز بيانات الموظف
      final employee = Employee(
        id: _selectedEmployee?.id,
        employeeId: _employeeIdController.text,
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        fullName: '${_firstNameController.text} ${_lastNameController.text}',
        email: _emailController.text.isEmpty ? null : _emailController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        position:
            _positionController.text.isEmpty ? null : _positionController.text,
        department: _departmentController.text.isEmpty
            ? null
            : _departmentController.text,
        basicSalary: double.tryParse(_salaryController.text) ?? 0,
        address:
            _addressController.text.isEmpty ? null : _addressController.text,
        nationalId: _nationalIdController.text.isEmpty
            ? null
            : _nationalIdController.text,
        bankAccount: _bankAccountController.text.isEmpty
            ? null
            : _bankAccountController.text,
        bankName:
            _bankNameController.text.isEmpty ? null : _bankNameController.text,
        hireDate: _hireDate,
        status: _status,
        gender: _gender,
      );

      // هنا سيتم استدعاء خدمة حفظ بيانات الموظف
      // مثال:
      // final employeeService = EmployeeService();
      // if (_selectedEmployee == null) {
      //   await employeeService.addEmployee(employee);
      // } else {
      //   await employeeService.updateEmployee(employee);
      // }

      // محاكاة عملية الحفظ
      await Future.delayed(const Duration(seconds: 1));

      // تحديث قائمة الموظفين
      if (_selectedEmployee == null) {
        // إضافة موظف جديد
        setState(() {
          _employees.add(employee);
          _filteredEmployees = List.from(_employees);
        });
        _showSuccessSnackBar('تمت إضافة الموظف بنجاح');
      } else {
        // تحديث موظف موجود
        final index = _employees.indexWhere((e) => e.id == employee.id);
        if (index != -1) {
          setState(() {
            _employees[index] = employee;
            _filteredEmployees = List.from(_employees);
          });
        }
        _showSuccessSnackBar('تم تحديث بيانات الموظف بنجاح');
      }

      // إغلاق النموذج
      _closeForm();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حفظ بيانات الموظف');
    } finally {
      setState(() {
        _isFormSubmitting = false;
      });
    }
  }

  /// حذف موظف
  Future<void> _deleteEmployee(String id) async {
    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا الموظف؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة حذف الموظف
      // مثال:
      // final employeeService = EmployeeService();
      // await employeeService.deleteEmployee(id);

      // محاكاة عملية الحذف
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _employees.removeWhere((employee) => employee.id == id);
        _filteredEmployees.removeWhere((employee) => employee.id == id);
        _isLoading = false;
      });

      _showSuccessSnackBar('تم حذف الموظف بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء حذف الموظف');
    }
  }

  /// طباعة بيانات الموظف
  Future<void> _printEmployee(Employee employee) async {
    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري إعداد تقرير الموظف للطباعة...');

    // هنا سيتم استدعاء خدمة الطباعة
    // مثال:
    // final printService = PrintService();
    // await printService.printEmployee(employee);

    // محاكاة عملية الطباعة
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم إرسال بيانات الموظف للطباعة بنجاح');
  }

  /// طباعة قائمة الموظفين
  Future<void> _printEmployeesList() async {
    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري إعداد قائمة الموظفين للطباعة...');

    // هنا سيتم استدعاء خدمة الطباعة
    // مثال:
    // final printService = PrintService();
    // await printService.printEmployeesList(_filteredEmployees);

    // محاكاة عملية الطباعة
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم إرسال قائمة الموظفين للطباعة بنجاح');
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
                fontSize: AppDimensions.defaultFontSize,
              ),
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        margin: AppDimensions.defaultPadding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
                fontSize: AppDimensions.defaultFontSize,
              ),
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        margin: AppDimensions.defaultPadding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }

  /// عرض رسالة معلومات
  void _showInfoSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة الموظفين',
        showBackButton: true,
        backgroundColor: DynamicColors.surface(context),
        actions: [
          AkIconButton(
            icon: Icons.refresh,
            tooltip: 'تحديث البيانات',
            onPressed: _loadEmployees,
            type: AkButtonType.secondary,
          ),
          AkIconButton(
            icon: Icons.print,
            tooltip: 'طباعة القائمة',
            onPressed: _printEmployeesList,
            type: AkButtonType.info,
          ),
        ],
      ),
      backgroundColor: DynamicColors.background(context),
      body: _isFormVisible ? _buildEmployeeForm() : _buildEmployeesList(),
      floatingActionButton: _isFormVisible
          ? null
          : AkFloatingButton(
              icon: Icons.add,
              onPressed: _setupAddEmployeeForm,
              tooltip: 'إضافة موظف جديد',
            ),
    );
  }

  /// بناء قائمة الموظفين
  Widget _buildEmployeesList() {
    if (_isLoading) {
      return const AkLoadingIndicator();
    }

    return Padding(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.medium,
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: _filteredEmployees.isEmpty
                ? const AkEmptyState(
                    message: 'لا توجد بيانات للعرض',
                    icon: Icons.people_outline,
                  )
                : _buildEmployeesTable(),
          ),
        ],
      ),
    );
  }

  /// بناء حقل البحث وأدوات التصفية
  Widget _buildSearchAndFilter() {
    return AkColumn(
      spacing: AkSpacingSize.small,
      children: [
        AkRow(
          spacing: AkSpacingSize.small,
          children: [
            Expanded(
              child: AkSearchInput(
                controller: _searchController,
                hint: 'بحث عن موظف...',
                onChanged: _searchEmployees,
              ),
            ),
            AkIconButton(
              icon:
                  _isFilterExpanded ? Icons.filter_list_off : Icons.filter_list,
              tooltip: 'خيارات التصفية',
              onPressed: () {
                setState(() {
                  _isFilterExpanded = !_isFilterExpanded;
                });
              },
              type: _isFilterExpanded
                  ? AkButtonType.primary
                  : AkButtonType.secondary,
            ),
          ],
        ),
        if (_isFilterExpanded) ...[
          AkRow(
            spacing: AkSpacingSize.medium,
            children: [
              Expanded(
                child: AkDropdownInput<String>(
                  label: 'الحالة',
                  value: _statusFilter,
                  items: _statusOptions.map((status) {
                    return DropdownMenuItem<String>(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _statusFilter = value;
                      });
                      _applyFilters(_searchController.text, _statusFilter,
                          _departmentFilter);
                    }
                  },
                  prefixIcon: Icons.filter_alt,
                ),
              ),
              Expanded(
                child: AkDropdownInput<String>(
                  label: 'القسم',
                  value: _departmentFilter,
                  items: _departments.map((department) {
                    return DropdownMenuItem<String>(
                      value: department,
                      child: Text(department),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _departmentFilter = value;
                      });
                      _applyFilters(_searchController.text, _statusFilter,
                          _departmentFilter);
                    }
                  },
                  prefixIcon: Icons.business,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء جدول الموظفين
  Widget _buildEmployeesTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: DataTableWidget<Employee>(
          columns: const [
            DataColumn(label: Text('رقم الموظف')),
            DataColumn(label: Text('الاسم')),
            DataColumn(label: Text('القسم')),
            DataColumn(label: Text('المنصب')),
            DataColumn(label: Text('الراتب')),
            DataColumn(label: Text('الحالة')),
            DataColumn(label: Text('الإجراءات')),
          ],
          items: _filteredEmployees,
          rowBuilder: (employee) {
            return DataRow(
              cells: [
                DataCell(Text(employee.employeeId)),
                DataCell(Text(employee.fullName)),
                DataCell(Text(employee.department ?? '')),
                DataCell(Text(employee.position ?? '')),
                DataCell(Text(employee.basicSalary.toString())),
                DataCell(Text(employee.status)),
                DataCell(_buildActionButtons(employee)),
              ],
            );
          },
        ),
      ),
    );
  }

  // تم إزالة الدوال غير المستخدمة

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(Employee employee) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.edit, color: AppColors.info),
          tooltip: 'تعديل',
          onPressed: () => _setupEditEmployeeForm(employee),
        ),
        IconButton(
          icon: const Icon(Icons.delete, color: AppColors.error),
          tooltip: 'حذف',
          onPressed: () => _deleteEmployee(employee.id),
        ),
        IconButton(
          icon: const Icon(Icons.print, color: AppColors.success),
          tooltip: 'طباعة',
          onPressed: () => _printEmployee(employee),
        ),
      ],
    );
  }

  /// بناء نموذج الموظف
  Widget _buildEmployeeForm() {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _selectedEmployee == null
                            ? 'إضافة موظف جديد'
                            : 'تعديل بيانات الموظف',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _closeForm,
                    ),
                  ],
                ),
                const Divider(),
                const SizedBox(height: AppDimensions.spacing16),
                // حقول النموذج
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _employeeIdController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الموظف *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => value == null || value.isEmpty
                            ? 'يرجى إدخال رقم الموظف'
                            : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التعيين *',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text:
                              '${_hireDate.day}/${_hireDate.month}/${_hireDate.year}',
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _hireDate,
                            firstDate: DateTime(1900),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _hireDate = date;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _firstNameController,
                        decoration: const InputDecoration(
                          labelText: 'الاسم الأول *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => value == null || value.isEmpty
                            ? 'يرجى إدخال الاسم الأول'
                            : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _lastNameController,
                        decoration: const InputDecoration(
                          labelText: 'الاسم الأخير *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => value == null || value.isEmpty
                            ? 'يرجى إدخال الاسم الأخير'
                            : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'البريد الإلكتروني',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.emailAddress,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الهاتف',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.phone,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _positionController,
                        decoration: const InputDecoration(
                          labelText: 'المسمى الوظيفي',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _departmentController,
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _salaryController,
                        decoration: const InputDecoration(
                          labelText: 'الراتب الأساسي *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) => value == null || value.isEmpty
                            ? 'يرجى إدخال الراتب الأساسي'
                            : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الجنس',
                          border: OutlineInputBorder(),
                        ),
                        value: _gender,
                        items: _genderOptions.map((gender) {
                          return DropdownMenuItem<String>(
                            value: gender,
                            child: Text(gender),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _gender = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الميلاد',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text: _birthDate != null
                              ? '${_birthDate!.day}/${_birthDate!.month}/${_birthDate!.year}'
                              : '',
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _birthDate ?? DateTime(1990),
                            firstDate: DateTime(1900),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _birthDate = date;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الحالة',
                          border: OutlineInputBorder(),
                        ),
                        value: _status,
                        items: const [
                          DropdownMenuItem<String>(
                            value: 'active',
                            child: Text('نشط'),
                          ),
                          DropdownMenuItem<String>(
                            value: 'inactive',
                            child: Text('غير نشط'),
                          ),
                          DropdownMenuItem<String>(
                            value: 'on_leave',
                            child: Text('في إجازة'),
                          ),
                          DropdownMenuItem<String>(
                            value: 'terminated',
                            child: Text('منتهي الخدمة'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _status = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                TextFormField(
                  controller: _addressController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _nationalIdController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الهوية',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _bankNameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم البنك',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _bankAccountController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الحساب البنكي',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: AppDimensions.spacing24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AkButton(
                      text: _selectedEmployee == null
                          ? 'إضافة الموظف'
                          : 'حفظ التغييرات',
                      icon: Icons.save,
                      onPressed: _saveEmployee,
                      type: AkButtonType.primary,
                      size: AkButtonSize.medium,
                    ),
                    const SizedBox(width: AppDimensions.spacing16),
                    AkButton(
                      text: 'إلغاء',
                      icon: Icons.cancel,
                      onPressed: _closeForm,
                      type: AkButtonType.secondary,
                      size: AkButtonSize.medium,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (_isFormSubmitting)
          Container(
            color: Colors.black54,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }
}
