import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

/// شاشة إضافة وتعديل الرواتب (نسخة مبسطة)
class PayrollFormScreenSimple extends StatefulWidget {
  final Map<String, dynamic>? payroll;

  const PayrollFormScreenSimple({Key? key, this.payroll}) : super(key: key);

  @override
  State<PayrollFormScreenSimple> createState() =>
      _PayrollFormScreenSimpleState();
}

class _PayrollFormScreenSimpleState extends State<PayrollFormScreenSimple> {
  final _formKey = GlobalKey<FormState>();

  // حقول النموذج
  final TextEditingController _employeeNameController = TextEditingController();
  final TextEditingController _salaryController = TextEditingController();
  final TextEditingController _bonusController = TextEditingController();
  final TextEditingController _deductionsController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // متغيرات الحالة
  // سيتم استخدام هذا المتغير في المستقبل عند تنفيذ وظائف متقدمة
  // DateTime _paymentDate = DateTime.now();
  String _selectedPaymentMethod = 'cash';
  bool _isPaid = false;
  final bool _isLoading = false;

  // قائمة ثابتة لطرق الدفع
  final List<Map<String, dynamic>> _paymentMethods = [
    {'value': 'cash', 'label': 'نقدي'},
    {'value': 'bank', 'label': 'تحويل بنكي'},
    {'value': 'cheque', 'label': 'شيك'},
  ];

  @override
  void initState() {
    super.initState();

    // If editing an existing payroll, populate the form
    if (widget.payroll != null) {
      _populateFormWithPayrollData();
    }
  }

  @override
  void dispose() {
    // التخلص من وحدات التحكم في النص عند إغلاق الشاشة
    _employeeNameController.dispose();
    _salaryController.dispose();
    _bonusController.dispose();
    _deductionsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // تعبئة النموذج ببيانات الراتب الحالي
  void _populateFormWithPayrollData() {
    final payroll = widget.payroll!;

    _employeeNameController.text = payroll['employee_name'] as String? ?? '';
    _salaryController.text = payroll['salary']?.toString() ?? '';
    _bonusController.text = payroll['bonus']?.toString() ?? '';
    _deductionsController.text = payroll['deductions']?.toString() ?? '';
    _notesController.text = payroll['notes'] as String? ?? '';

    // تم تعليق هذا الكود لأن المتغير _paymentDate غير مستخدم حاليًا
    // if (payroll['payment_date'] != null) {
    //   _paymentDate = DateTime.parse(payroll['payment_date']);
    // }

    _selectedPaymentMethod = payroll['payment_method'] as String? ?? 'cash';
    _isPaid = payroll['is_paid'] == 1;
  }

  // حساب إجمالي الراتب
  double _calculateTotalSalary() {
    double salary = double.tryParse(_salaryController.text) ?? 0;
    double bonus = double.tryParse(_bonusController.text) ?? 0;
    double deductions = double.tryParse(_deductionsController.text) ?? 0;
    return salary + bonus - deductions;
  }

  // حفظ الراتب
  void _savePayroll() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ الراتب بنجاح')),
    );

    // العودة إلى الشاشة السابقة
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AkLoadingOverlay(
      isLoading: _isLoading,
      child: Scaffold(
        appBar: AkAppBar(
          title: widget.payroll == null ? 'إضافة راتب جديد' : 'تعديل الراتب',
          actions: [
            if (widget.payroll != null)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  // عرض مربع حوار تأكيد الحذف
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('تأكيد الحذف'),
                      content: const Text('هل تريد حذف هذا الراتب؟'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('إلغاء'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            // تنفيذ عملية الحذف
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.error,
                          ),
                          child: const Text('حذف'),
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقة البيانات الأساسية
                AkCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'بيانات الراتب',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // اسم الموظف
                          TextFormField(
                            controller: _employeeNameController,
                            decoration: const InputDecoration(
                              labelText: 'اسم الموظف *',
                              hintText: 'أدخل اسم الموظف',
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'اسم الموظف مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // الراتب الأساسي
                          TextFormField(
                            controller: _salaryController,
                            decoration: const InputDecoration(
                              labelText: 'الراتب الأساسي *',
                              hintText: '0.00',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9.]')),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الراتب الأساسي مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // المكافآت
                          TextFormField(
                            controller: _bonusController,
                            decoration: const InputDecoration(
                              labelText: 'المكافآت',
                              hintText: '0.00',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9.]')),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // الخصومات
                          TextFormField(
                            controller: _deductionsController,
                            decoration: const InputDecoration(
                              labelText: 'الخصومات',
                              hintText: '0.00',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9.]')),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // إجمالي الراتب
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.lightTextSecondary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('إجمالي الراتب:'),
                                Text(
                                  _calculateTotalSalary().toStringAsFixed(2),
                                  style: const AppTypography(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // طريقة الدفع
                          DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'طريقة الدفع *',
                              hintText: 'اختر طريقة الدفع',
                            ),
                            value: _selectedPaymentMethod,
                            items: _paymentMethods.map((method) {
                              return DropdownMenuItem<String>(
                                value: method['value'],
                                child: Text(method['label']),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedPaymentMethod = value!;
                              });
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'طريقة الدفع مطلوبة';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // الملاحظات
                          TextFormField(
                            controller: _notesController,
                            decoration: const InputDecoration(
                              labelText: 'ملاحظات',
                              hintText: 'أدخل أي ملاحظات إضافية',
                            ),
                            maxLines: 3,
                          ),
                          const SizedBox(height: AppDimensions.spacing16),
                          // حالة الدفع
                          SwitchListTile(
                            title: const Text('تم الدفع'),
                            value: _isPaid,
                            onChanged: (value) {
                              setState(() {
                                _isPaid = value;
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing16),
                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    AkButton(
                      text: 'إلغاء',
                      onPressed: () => Navigator.of(context).pop(),
                      type: AkButtonType.secondary,
                    ),
                    const SizedBox(width: 16),
                    AkButton(
                      text: 'حفظ',
                      onPressed: _savePayroll,
                      type: AkButtonType.primary,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
