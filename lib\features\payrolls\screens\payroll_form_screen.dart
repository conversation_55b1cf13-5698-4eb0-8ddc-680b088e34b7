import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/index.dart';
import 'package:tajer_plus/core/utils/index.dart';
import '../../../core/widgets/index.dart';
import 'package:tajer_plus/core/models/employee.dart';
import 'package:tajer_plus/core/models/payroll.dart';
import 'package:tajer_plus/core/models/payroll_detail.dart';
import 'package:tajer_plus/core/models/payroll_status.dart';

/// شاشة إضافة أو تعديل راتب
class PayrollFormScreen extends StatefulWidget {
  final Payroll? payroll;

  const PayrollFormScreen({Key? key, this.payroll}) : super(key: key);

  @override
  State<PayrollFormScreen> createState() => _PayrollFormScreenState();
}

class _PayrollFormScreenState extends State<PayrollFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _payrollNumberController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _periodStartDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _periodEndDate = DateTime.now();
  DateTime _paymentDate = DateTime.now().add(const Duration(days: 5));
  PayrollStatus _status = PayrollStatus.draft;
  String? _selectedBranchId;
  List<Map<String, dynamic>> _branches = [];
  List<Employee> _employees = [];
  List<PayrollDetail> _payrollDetails = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadBranches();
    _loadEmployees();

    if (widget.payroll != null) {
      _initializeFormWithPayrollData();
    } else {
      // توليد رقم راتب جديد
      _generateNewPayrollNumber();
    }
  }

  @override
  void dispose() {
    _payrollNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الفروع
  Future<void> _loadBranches() async {
    // هنا سيتم استدعاء خدمة جلب بيانات الفروع
    // مثال:
    // final branchService = BranchService();
    // final branches = await branchService.getBranches();

    // بيانات تجريبية للعرض
    await Future.delayed(const Duration(milliseconds: 500));
    final dummyBranches = [
      {'id': '1', 'name': 'الفرع الرئيسي'},
      {'id': '2', 'name': 'فرع المدينة'},
      {'id': '3', 'name': 'فرع الشمال'},
    ];

    setState(() {
      _branches = dummyBranches;
      if (_selectedBranchId == null && _branches.isNotEmpty) {
        _selectedBranchId = _branches[0]['id'];
      }
    });
  }

  /// تحميل قائمة الموظفين
  Future<void> _loadEmployees() async {
    // هنا سيتم استدعاء خدمة جلب بيانات الموظفين
    // مثال:
    // final employeeService = EmployeeService();
    // final employees = await employeeService.getEmployees();

    // بيانات تجريبية للعرض
    await Future.delayed(const Duration(milliseconds: 500));
    final dummyEmployees = [
      Employee(
        id: '1',
        employeeId: 'EMP001',
        firstName: 'أحمد',
        lastName: 'محمد',
        fullName: 'أحمد محمد',
        position: 'مدير مبيعات',
        department: 'المبيعات',
        basicSalary: 5000,
        hireDate: DateTime.now(),
        status: 'active',
      ),
      Employee(
        id: '2',
        employeeId: 'EMP002',
        firstName: 'سارة',
        lastName: 'محمد',
        fullName: 'سارة أحمد',
        position: 'محاسب',
        department: 'المالية',
        basicSalary: 4500,
        hireDate: DateTime.now(),
        status: 'active',
      ),
      Employee(
        id: '3',
        employeeId: 'EMP003',
        firstName: 'محمد',
        lastName: 'علي',
        fullName: 'محمد علي',
        position: 'مندوب مبيعات',
        department: 'المبيعات',
        basicSalary: 3500,
        hireDate: DateTime.now(),
        status: 'active',
      ),
    ];

    setState(() {
      _employees = dummyEmployees;

      // إذا كان إنشاء راتب جديد، نقوم بإضافة جميع الموظفين تلقائياً
      if (widget.payroll == null) {
        _payrollDetails = _employees.map((employee) {
          return PayrollDetail(
            id: null,
            payrollId: '',
            employeeId: employee.id,
            employeeNumber: employee.employeeId,
            employeeName: employee.fullName,
            basicSalary: employee.basicSalary ?? 0.0,
            netSalary: employee.basicSalary ?? 0.0,
          );
        }).toList();
      }
    });
  }

  /// تعبئة النموذج ببيانات الراتب الحالي للتعديل
  void _initializeFormWithPayrollData() {
    final payroll = widget.payroll!;
    _payrollNumberController.text = payroll.payrollNumber;
    _notesController.text = payroll.notes ?? '';
    _periodStartDate = payroll.periodStartDate;
    _periodEndDate = payroll.periodEndDate;
    _paymentDate = payroll.paymentDate;
    _status = payroll.status;
    _selectedBranchId = payroll.branchId;

    // تحميل تفاصيل الراتب إذا كانت متوفرة
    if (payroll.details != null && payroll.details!.isNotEmpty) {
      _payrollDetails = payroll.details!;
    }
  }

  /// توليد رقم راتب جديد
  void _generateNewPayrollNumber() {
    // هنا يمكن استدعاء خدمة لتوليد رقم راتب جديد
    // مثال بسيط:
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');
    final random = (1000 + now.millisecond).toString().substring(1);
    _payrollNumberController.text = 'PAY-$year$month-$random';
  }

  /// حساب صافي الراتب
  double _calculateNetSalary(PayrollDetail detail) {
    return detail.basicSalary +
        detail.allowances +
        detail.overtime +
        detail.bonus -
        detail.deductions;
  }

  /// تحديث تفاصيل الراتب
  void _updatePayrollDetail(int index, PayrollDetail updatedDetail) {
    setState(() {
      _payrollDetails[index] = updatedDetail.copyWith(
        netSalary: _calculateNetSalary(updatedDetail),
      );
    });
  }

  /// حفظ بيانات الراتب
  Future<void> _savePayroll() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تجهيز بيانات الراتب
      // final newPayroll = Payroll(
      //   id: widget.payroll?.id,
      //   payrollNumber: _payrollNumberController.text,
      //   periodStartDate: _periodStartDate,
      //   periodEndDate: _periodEndDate,
      //   paymentDate: _paymentDate,
      //   status: _status,
      //   notes: _notesController.text,
      //   branchId: _selectedBranchId,
      //   branchName: _branches.firstWhere((b) => b['id'] == _selectedBranchId,
      //       orElse: () => {'name': ''})['name'],
      //   details: _payrollDetails.map((detail) {
      //     return detail.copyWith(
      //       payrollId: widget.payroll?.id ?? '',
      //     );
      //   }).toList(),
      //   createdAt: widget.payroll?.createdAt,
      //   updatedAt: DateTime.now(),
      // );

      // هنا سيتم استدعاء خدمة حفظ بيانات الراتب
      // مثال:
      // final payrollService = PayrollService();
      // if (widget.payroll == null) {
      //   await payrollService.addPayroll(newPayroll);
      // } else {
      //   await payrollService.updatePayroll(newPayroll);
      // }

      // محاكاة تأخير الحفظ
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.payroll == null
                  ? 'تم إضافة الراتب بنجاح'
                  : 'تم تحديث بيانات الراتب بنجاح',
            ),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            widget.payroll == null ? 'إضافة راتب جديد' : 'تعديل بيانات الراتب'),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPayrollInfoCard(),
            const SizedBox(height: AppDimensions.spacing16),
            _buildEmployeesPayrollCard(),
            const SizedBox(height: AppDimensions.spacing24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildPayrollInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الراتب',
              style: AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppDimensions.spacing16),
            Layout.isDesktop() || Layout.isTablet()
                ? _buildDesktopPayrollInfoFields()
                : _buildMobilePayrollInfoFields(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopPayrollInfoFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _payrollNumberController,
                decoration: const InputDecoration(
                  labelText: 'رقم الراتب *',
                  prefixIcon: Icon(Icons.numbers),
                ),
                validator: Validators.required('رقم الراتب'),
                readOnly: widget.payroll !=
                    null, // لا يمكن تعديل رقم الراتب بعد إنشائه
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الفرع *',
                  prefixIcon: Icon(Icons.business),
                ),
                value: _selectedBranchId,
                items: _branches.map((branch) {
                  return DropdownMenuItem<String>(
                    value: branch['id'],
                    child: Text(branch['name']),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedBranchId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء اختيار الفرع';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'تاريخ بداية الفترة *',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                controller: TextEditingController(
                  text: DateFormat('yyyy/MM/dd').format(_periodStartDate),
                ),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _periodStartDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    setState(() {
                      _periodStartDate = date;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'تاريخ نهاية الفترة *',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                controller: TextEditingController(
                  text: DateFormat('yyyy/MM/dd').format(_periodEndDate),
                ),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _periodEndDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    setState(() {
                      _periodEndDate = date;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'تاريخ الدفع *',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                controller: TextEditingController(
                  text: DateFormat('yyyy/MM/dd').format(_paymentDate),
                ),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _paymentDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    setState(() {
                      _paymentDate = date;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AkDropdownInput<PayrollStatus>(
                label: 'حالة الراتب',
                value: _status,
                items: const [
                  DropdownMenuItem(
                      value: PayrollStatus.draft, child: Text('مسودة')),
                  DropdownMenuItem(
                      value: PayrollStatus.approved, child: Text('معتمد')),
                  DropdownMenuItem(
                      value: PayrollStatus.paid, child: Text('مدفوع')),
                  DropdownMenuItem(
                      value: PayrollStatus.cancelled, child: Text('ملغي')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _status = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildMobilePayrollInfoFields() {
    return Column(
      children: [
        TextFormField(
          controller: _payrollNumberController,
          decoration: const InputDecoration(
            labelText: 'رقم الراتب *',
            prefixIcon: Icon(Icons.numbers),
          ),
          validator: Validators.required('رقم الراتب'),
          readOnly:
              widget.payroll != null, // لا يمكن تعديل رقم الراتب بعد إنشائه
        ),
        const SizedBox(height: AppDimensions.spacing16),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'الفرع *',
            prefixIcon: Icon(Icons.business),
          ),
          value: _selectedBranchId,
          items: _branches.map((branch) {
            return DropdownMenuItem<String>(
              value: branch['id'],
              child: Text(branch['name']),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedBranchId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء اختيار الفرع';
            }
            return null;
          },
        ),
        const SizedBox(height: AppDimensions.spacing16),
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'تاريخ بداية الفترة *',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.calendar_today),
          ),
          readOnly: true,
          controller: TextEditingController(
            text: DateFormat('yyyy/MM/dd').format(_periodStartDate),
          ),
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _periodStartDate,
              firstDate: DateTime(2020),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _periodStartDate = date;
              });
            }
          },
        ),
        const SizedBox(height: AppDimensions.spacing16),
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'تاريخ نهاية الفترة *',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.calendar_today),
          ),
          readOnly: true,
          controller: TextEditingController(
            text: DateFormat('yyyy/MM/dd').format(_periodEndDate),
          ),
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _periodEndDate,
              firstDate: DateTime(2020),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _periodEndDate = date;
              });
            }
          },
        ),
        const SizedBox(height: AppDimensions.spacing16),
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'تاريخ الدفع *',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.calendar_today),
          ),
          readOnly: true,
          controller: TextEditingController(
            text: DateFormat('yyyy/MM/dd').format(_paymentDate),
          ),
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _paymentDate,
              firstDate: DateTime(2020),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _paymentDate = date;
              });
            }
          },
        ),
        const SizedBox(height: AppDimensions.spacing16),
        AkDropdownInput<PayrollStatus>(
          label: 'حالة الراتب',
          value: _status,
          items: const [
            DropdownMenuItem(value: PayrollStatus.draft, child: Text('مسودة')),
            DropdownMenuItem(
                value: PayrollStatus.approved, child: Text('معتمد')),
            DropdownMenuItem(value: PayrollStatus.paid, child: Text('مدفوع')),
            DropdownMenuItem(
                value: PayrollStatus.cancelled, child: Text('ملغي')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _status = value;
              });
            }
          },
        ),
        const SizedBox(height: AppDimensions.spacing16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildEmployeesPayrollCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تفاصيل رواتب الموظفين',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  'إجمالي الرواتب: ${_calculateTotalSalaries()} ${AppConstants.appCurrency}',
                  style: const AppTypography(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing16),
            _buildEmployeesPayrollTable(),
          ],
        ),
      ),
    );
  }

  double _calculateTotalSalaries() {
    return _payrollDetails.fold(0, (sum, detail) => sum + detail.netSalary);
  }

  Widget _buildEmployeesPayrollTable() {
    if (_payrollDetails.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('لا يوجد موظفين في كشف الراتب'),
        ),
      );
    }

    return Layout.isDesktop() || Layout.isTablet()
        ? _buildDesktopEmployeesTable()
        : _buildMobileEmployeesList();
  }

  Widget _buildDesktopEmployeesTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الموظف')),
          DataColumn(label: Text('الراتب الأساسي')),
          DataColumn(label: Text('البدلات')),
          DataColumn(label: Text('الخصومات')),
          DataColumn(label: Text('العمل الإضافي')),
          DataColumn(label: Text('المكافآت')),
          DataColumn(label: Text('صافي الراتب')),
          DataColumn(label: Text('إجراءات')),
        ],
        rows: List.generate(_payrollDetails.length, (index) {
          final detail = _payrollDetails[index];
          return DataRow(
            cells: [
              DataCell(Text(detail.employeeName)),
              DataCell(Text('${detail.basicSalary}')),
              DataCell(
                TextFormField(
                  initialValue: detail.allowances.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onChanged: (value) {
                    final allowances = double.tryParse(value) ?? 0.0;
                    _updatePayrollDetail(
                      index,
                      detail.copyWith(allowances: allowances),
                    );
                  },
                ),
              ),
              DataCell(
                TextFormField(
                  initialValue: detail.deductions.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onChanged: (value) {
                    final deductions = double.tryParse(value) ?? 0.0;
                    _updatePayrollDetail(
                      index,
                      detail.copyWith(deductions: deductions),
                    );
                  },
                ),
              ),
              DataCell(
                TextFormField(
                  initialValue: detail.overtime.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onChanged: (value) {
                    final overtime = double.tryParse(value) ?? 0.0;
                    _updatePayrollDetail(
                      index,
                      detail.copyWith(overtime: overtime),
                    );
                  },
                ),
              ),
              DataCell(
                TextFormField(
                  initialValue: detail.bonus.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onChanged: (value) {
                    final bonus = double.tryParse(value) ?? 0.0;
                    _updatePayrollDetail(
                      index,
                      detail.copyWith(bonus: bonus),
                    );
                  },
                ),
              ),
              DataCell(Text(
                '${detail.netSalary}',
                style: const AppTypography(fontWeight: FontWeight.bold),
              )),
              DataCell(
                IconButton(
                  icon: const Icon(Icons.edit,
                      color: AppColors.lightTextSecondary, size: 20),
                  onPressed: () =>
                      _showEmployeePayrollDetailDialog(detail, index),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildMobileEmployeesList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _payrollDetails.length,
      itemBuilder: (context, index) {
        final detail = _payrollDetails[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            title: Text(
              detail.employeeName,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'صافي الراتب: ${detail.netSalary} ${AppConstants.appCurrency}',
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    _buildPayrollDetailRow(
                        'الراتب الأساسي', detail.basicSalary.toString()),
                    _buildPayrollDetailEditRow(
                      'البدلات',
                      detail.allowances.toString(),
                      (value) {
                        final allowances = double.tryParse(value) ?? 0.0;
                        _updatePayrollDetail(
                          index,
                          detail.copyWith(allowances: allowances),
                        );
                      },
                    ),
                    _buildPayrollDetailEditRow(
                      'الخصومات',
                      detail.deductions.toString(),
                      (value) {
                        final deductions = double.tryParse(value) ?? 0.0;
                        _updatePayrollDetail(
                          index,
                          detail.copyWith(deductions: deductions),
                        );
                      },
                    ),
                    _buildPayrollDetailEditRow(
                      'العمل الإضافي',
                      detail.overtime.toString(),
                      (value) {
                        final overtime = double.tryParse(value) ?? 0.0;
                        _updatePayrollDetail(
                          index,
                          detail.copyWith(overtime: overtime),
                        );
                      },
                    ),
                    _buildPayrollDetailEditRow(
                      'المكافآت',
                      detail.bonus.toString(),
                      (value) {
                        final bonus = double.tryParse(value) ?? 0.0;
                        _updatePayrollDetail(
                          index,
                          detail.copyWith(bonus: bonus),
                        );
                      },
                    ),
                    const Divider(),
                    _buildPayrollDetailRow(
                      'صافي الراتب',
                      '${detail.netSalary} ${AppConstants.appCurrency}',
                      isBold: true,
                    ),
                    const SizedBox(height: AppDimensions.spacing8),
                    ElevatedButton.icon(
                      onPressed: () =>
                          _showEmployeePayrollDetailDialog(detail, index),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل تفاصيل الراتب'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPayrollDetailRow(String label, String value,
      {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: AppTypography(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal),
          ),
        ],
      ),
    );
  }

  Widget _buildPayrollDetailEditRow(
      String label, String initialValue, Function(String) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          SizedBox(
            width: 100,
            child: TextFormField(
              initialValue: initialValue,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              ),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  void _showEmployeePayrollDetailDialog(PayrollDetail detail, int index) {
    final allowancesController =
        TextEditingController(text: detail.allowances.toString());
    final deductionsController =
        TextEditingController(text: detail.deductions.toString());
    final overtimeController =
        TextEditingController(text: detail.overtime.toString());
    final bonusController =
        TextEditingController(text: detail.bonus.toString());
    final paymentMethodController =
        TextEditingController(text: detail.metadata?['paymentMethod'] ?? '');
    final paymentReferenceController =
        TextEditingController(text: detail.metadata?['paymentReference'] ?? '');
    final notesController =
        TextEditingController(text: detail.metadata?['notes'] ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل راتب ${detail.employeeName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                readOnly: true,
                initialValue: detail.basicSalary.toString(),
                decoration: const InputDecoration(
                  labelText: 'الراتب الأساسي',
                ),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: allowancesController,
                decoration: const InputDecoration(
                  labelText: 'البدلات',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: deductionsController,
                decoration: const InputDecoration(
                  labelText: 'الخصومات',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: overtimeController,
                decoration: const InputDecoration(
                  labelText: 'العمل الإضافي',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: bonusController,
                decoration: const InputDecoration(
                  labelText: 'المكافآت',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: paymentMethodController,
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                ),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: paymentReferenceController,
                decoration: const InputDecoration(
                  labelText: 'مرجع الدفع',
                ),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              TextFormField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final updatedDetail = detail.copyWith(
                allowances: double.tryParse(allowancesController.text) ?? 0.0,
                deductions: double.tryParse(deductionsController.text) ?? 0.0,
                overtime: double.tryParse(overtimeController.text) ?? 0.0,
                bonus: double.tryParse(bonusController.text) ?? 0.0,
                metadata: {
                  'paymentMethod': paymentMethodController.text,
                  'paymentReference': paymentReferenceController.text,
                  'notes': notesController.text,
                },
              );

              _updatePayrollDetail(index, updatedDetail);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        ElevatedButton.icon(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.cancel),
          label: const Text('إلغاء'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.lightTextSecondary,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _isLoading ? null : _savePayroll,
          icon: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.save),
          label: Text(widget.payroll == null ? 'إضافة' : 'حفظ التغييرات'),
        ),
      ],
    );
  }
}
