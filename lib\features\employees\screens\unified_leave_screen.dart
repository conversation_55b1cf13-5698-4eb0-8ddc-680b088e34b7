import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:tajer_plus/core/models/employee.dart';
import 'package:tajer_plus/core/models/leave.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

// استخدام نموذج الإجازة من المسار الرئيسي

/// شاشة موحدة لإدارة الإجازات والغياب (عرض، إضافة، تعديل، حذف، طباعة)
/// تم تصميمها لتكون أكثر كفاءة وسرعة في الاستخدام
class UnifiedLeaveScreen extends StatefulWidget {
  const UnifiedLeaveScreen({Key? key}) : super(key: key);

  @override
  State<UnifiedLeaveScreen> createState() => _UnifiedLeaveScreenState();
}

class _UnifiedLeaveScreenState extends State<UnifiedLeaveScreen>
    with SingleTickerProviderStateMixin {
  // حالة الشاشة
  bool _isLoading = true;
  bool _isFormVisible = false;
  bool _isFormSubmitting = false;
  bool _isFilterExpanded = false;
  Leave? _selectedLeave;

  // وحدات التحكم للبحث والتصفية
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  String _statusFilter = 'الكل';
  String _typeFilter = 'الكل';

  // وحدات التحكم لنموذج الإجازة
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 1));
  String _leaveType = 'إجازة سنوية';
  String _status = 'قيد المراجعة';
  String? _selectedEmployeeId;

  // قوائم البيانات
  List<Leave> _leaves = [];
  List<Leave> _filteredLeaves = [];
  List<Employee> _employees = [];
  final List<String> _leaveTypes = [
    'إجازة سنوية',
    'إجازة مرضية',
    'إجازة بدون راتب',
    'إجازة طارئة',
    'إجازة أمومة'
  ];
  final List<String> _statusOptions = [
    'الكل',
    'قيد المراجعة',
    'معتمدة',
    'مرفوضة'
  ];

  // متحكم التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadLeaves();
    _loadEmployees();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _notesController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الإجازات
  Future<void> _loadLeaves() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة جلب بيانات الإجازات
      // مثال:
      // final leaveService = LeaveService();
      // final leaves = await leaveService.getLeaves();

      // بيانات تجريبية للعرض
      await Future.delayed(const Duration(seconds: 1));
      final now = DateTime.now();
      final dummyLeaves = [
        Leave(
          id: '1',
          employeeId: '1',
          employeeName: 'أحمد محمد',
          leaveType: 'إجازة سنوية',
          startDate: DateTime(now.year, now.month, now.day),
          endDate: DateTime(now.year, now.month, now.day + 5),
          days: 6,
          status: 'معتمدة',
          notes: 'إجازة سنوية مستحقة',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
        ),
        Leave(
          id: '2',
          employeeId: '2',
          employeeName: 'سارة أحمد',
          leaveType: 'إجازة مرضية',
          startDate: DateTime(now.year, now.month, now.day - 2),
          endDate: DateTime(now.year, now.month, now.day + 1),
          days: 4,
          status: 'قيد المراجعة',
          notes: 'بسبب المرض',
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
        Leave(
          id: '3',
          employeeId: '3',
          employeeName: 'محمد علي',
          leaveType: 'إجازة طارئة',
          startDate: DateTime(now.year, now.month, now.day - 1),
          endDate: DateTime(now.year, now.month, now.day),
          days: 2,
          status: 'مرفوضة',
          notes: 'ظروف عائلية طارئة',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ];

      setState(() {
        _leaves = dummyLeaves;
        _filteredLeaves = dummyLeaves;
        _isLoading = false;
      });
    } catch (e) {
      // معالجة الأخطاء
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل بيانات الإجازات');
    }
  }

  /// تحميل قائمة الموظفين
  Future<void> _loadEmployees() async {
    try {
      // هنا سيتم استدعاء خدمة جلب بيانات الموظفين
      // مثال:
      // final employeeService = EmployeeService();
      // final employees = await employeeService.getEmployees();

      // بيانات تجريبية للعرض
      await Future.delayed(const Duration(milliseconds: 500));
      final dummyEmployees = [
        Employee(
          id: '1',
          employeeId: 'EMP001',
          fullName: 'أحمد محمد',
          firstName: 'أحمد',
          lastName: 'محمد',
          position: 'مدير مبيعات',
          department: 'المبيعات',
          basicSalary: 5000,
          hireDate: DateTime.now(),
        ),
        Employee(
          id: '2',
          employeeId: 'EMP002',
          fullName: 'سارة أحمد',
          firstName: 'سارة',
          lastName: 'أحمد',
          position: 'محاسب',
          department: 'المالية',
          basicSalary: 4500,
          hireDate: DateTime.now(),
        ),
        Employee(
          id: '3',
          employeeId: 'EMP003',
          fullName: 'محمد علي',
          firstName: 'محمد',
          lastName: 'علي',
          position: 'مندوب مبيعات',
          department: 'المبيعات',
          basicSalary: 3500,
          hireDate: DateTime.now(),
        ),
      ];

      setState(() {
        _employees = dummyEmployees;
        if (_selectedEmployeeId == null && _employees.isNotEmpty) {
          _selectedEmployeeId = _employees[0].id;
        }
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل بيانات الموظفين');
    }
  }

  /// البحث في قائمة الإجازات
  void _searchLeaves(String query) {
    _applyFilters(query, _statusFilter, _typeFilter);
  }

  /// تطبيق التصفية على قائمة الإجازات
  void _applyFilters(String query, String statusFilter, String typeFilter) {
    List<Leave> filtered = _leaves;

    // تطبيق تصفية البحث
    if (query.isNotEmpty) {
      final searchLower = query.toLowerCase();
      filtered = filtered.where((leave) {
        final employeeName = leave.employeeName.toLowerCase();
        final leaveType = leave.leaveType.toLowerCase();
        final notes = leave.notes?.toLowerCase() ?? '';

        return employeeName.contains(searchLower) ||
            leaveType.contains(searchLower) ||
            notes.contains(searchLower);
      }).toList();
    }

    // تطبيق تصفية الحالة
    if (statusFilter != 'الكل') {
      filtered =
          filtered.where((leave) => leave.status == statusFilter).toList();
    }

    // تطبيق تصفية نوع الإجازة
    if (typeFilter != 'الكل') {
      filtered =
          filtered.where((leave) => leave.leaveType == typeFilter).toList();
    }

    setState(() {
      _filteredLeaves = filtered;
    });
  }

  /// حساب عدد أيام الإجازة
  int _calculateLeaveDays(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inDays + 1;
  }

  /// إعداد نموذج إضافة إجازة جديدة
  void _setupAddLeaveForm() {
    _clearForm();
    setState(() {
      _selectedLeave = null;
      _isFormVisible = true;
    });
  }

  /// إعداد نموذج تعديل إجازة
  void _setupEditLeaveForm(Leave leave) {
    _clearForm();
    _startDate = leave.startDate;
    _endDate = leave.endDate;
    _leaveType = leave.leaveType;
    _status = leave.status;
    _selectedEmployeeId = leave.employeeId;
    _notesController.text = leave.notes ?? '';

    setState(() {
      _selectedLeave = leave;
      _isFormVisible = true;
    });
  }

  /// مسح النموذج
  void _clearForm() {
    _formKey.currentState?.reset();
    _notesController.clear();
    _startDate = DateTime.now();
    _endDate = DateTime.now().add(const Duration(days: 1));
    _leaveType = 'إجازة سنوية';
    _status = 'قيد المراجعة';
    if (_employees.isNotEmpty) {
      _selectedEmployeeId = _employees[0].id;
    }
  }

  /// إغلاق النموذج
  void _closeForm() {
    setState(() {
      _isFormVisible = false;
      _selectedLeave = null;
    });
  }

  /// حفظ بيانات الإجازة
  Future<void> _saveLeave() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من صحة التواريخ
    if (_endDate.isBefore(_startDate)) {
      _showErrorSnackBar('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
      return;
    }

    setState(() {
      _isFormSubmitting = true;
    });

    try {
      // الحصول على اسم الموظف
      final employee = _employees.firstWhere(
        (e) => e.id == _selectedEmployeeId,
        orElse: () => Employee(
          id: '',
          employeeId: '',
          fullName: 'غير معروف',
          firstName: '',
          lastName: '',
          hireDate: DateTime.now(),
          basicSalary: 0,
        ),
      );

      // تجهيز بيانات الإجازة
      final leave = Leave(
        id: _selectedLeave?.id,
        employeeId: _selectedEmployeeId!,
        employeeName: employee.fullName,
        leaveType: _leaveType,
        startDate: _startDate,
        endDate: _endDate,
        days: _calculateLeaveDays(_startDate, _endDate),
        status: _status,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        createdAt: _selectedLeave?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // هنا سيتم استدعاء خدمة حفظ بيانات الإجازة
      // مثال:
      // final leaveService = LeaveService();
      // if (_selectedLeave == null) {
      //   await leaveService.addLeave(leave);
      // } else {
      //   await leaveService.updateLeave(leave);
      // }

      // محاكاة عملية الحفظ
      await Future.delayed(const Duration(seconds: 1));

      // تحديث قائمة الإجازات
      if (_selectedLeave == null) {
        // إضافة إجازة جديدة
        setState(() {
          _leaves.add(leave);
          _filteredLeaves = List.from(_leaves);
        });
        _showSuccessSnackBar('تمت إضافة الإجازة بنجاح');
      } else {
        // تحديث إجازة موجودة
        final index = _leaves.indexWhere((e) => e.id == leave.id);
        if (index != -1) {
          setState(() {
            _leaves[index] = leave;
            _filteredLeaves = List.from(_leaves);
          });
        }
        _showSuccessSnackBar('تم تحديث بيانات الإجازة بنجاح');
      }

      // إغلاق النموذج
      _closeForm();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حفظ بيانات الإجازة');
    } finally {
      setState(() {
        _isFormSubmitting = false;
      });
    }
  }

  /// حذف إجازة
  Future<void> _deleteLeave(String id) async {
    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذه الإجازة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة حذف الإجازة
      // مثال:
      // final leaveService = LeaveService();
      // await leaveService.deleteLeave(id);

      // محاكاة عملية الحذف
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _leaves.removeWhere((leave) => leave.id == id);
        _filteredLeaves.removeWhere((leave) => leave.id == id);
        _isLoading = false;
      });

      _showSuccessSnackBar('تم حذف الإجازة بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء حذف الإجازة');
    }
  }

  // تم حذف دالة _changeLeaveStatus لأنها غير مستخدمة

  /// طباعة بيانات الإجازة
  Future<void> _printLeave(Leave leave) async {
    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري إعداد تقرير الإجازة للطباعة...');

    // هنا سيتم استدعاء خدمة الطباعة
    // مثال:
    // final printService = PrintService();
    // await printService.printLeave(leave);

    // محاكاة عملية الطباعة
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم إرسال بيانات الإجازة للطباعة بنجاح');
  }

  /// طباعة قائمة الإجازات
  Future<void> _printLeavesList() async {
    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري إعداد قائمة الإجازات للطباعة...');

    // هنا سيتم استدعاء خدمة الطباعة
    // مثال:
    // final printService = PrintService();
    // await printService.printLeavesList(_filteredLeaves);

    // محاكاة عملية الطباعة
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم إرسال قائمة الإجازات للطباعة بنجاح');
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  /// عرض رسالة معلومات
  void _showInfoSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة الإجازات والغياب',
        showBackButton: true,
        backgroundColor: DynamicColors.surface(context),
        actions: [
          AkIconButton(
            icon: Icons.refresh,
            tooltip: 'تحديث البيانات',
            onPressed: _loadLeaves,
            type: AkButtonType.secondary,
          ),
          AkIconButton(
            icon: Icons.print,
            tooltip: 'طباعة القائمة',
            onPressed: _printLeavesList,
            type: AkButtonType.info,
          ),
        ],
      ),
      backgroundColor: DynamicColors.background(context),
      body: _isFormVisible ? _buildLeaveForm() : _buildLeavesList(),
      floatingActionButton: _isFormVisible
          ? null
          : AkFloatingButton(
              icon: Icons.add,
              onPressed: _setupAddLeaveForm,
              tooltip: 'إضافة إجازة جديدة',
            ),
    );
  }

  /// بناء قائمة الإجازات
  Widget _buildLeavesList() {
    if (_isLoading) {
      return const AkLoadingIndicator();
    }

    return Padding(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.medium,
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: _filteredLeaves.isEmpty
                ? const AkEmptyState(
                    message: 'لا توجد بيانات للعرض',
                    icon: Icons.event_busy,
                  )
                : _buildLeavesTable(),
          ),
        ],
      ),
    );
  }

  /// بناء حقل البحث وأدوات التصفية
  Widget _buildSearchAndFilter() {
    return AkColumn(
      spacing: AkSpacingSize.small,
      children: [
        AkRow(
          spacing: AkSpacingSize.small,
          children: [
            Expanded(
              child: AkSearchInput(
                controller: _searchController,
                hint: 'بحث عن إجازة...',
                onChanged: _searchLeaves,
              ),
            ),
            AkIconButton(
              icon:
                  _isFilterExpanded ? Icons.filter_list_off : Icons.filter_list,
              tooltip: 'خيارات التصفية',
              onPressed: () {
                setState(() {
                  _isFilterExpanded = !_isFilterExpanded;
                });
              },
              type: _isFilterExpanded
                  ? AkButtonType.primary
                  : AkButtonType.secondary,
            ),
          ],
        ),
        if (_isFilterExpanded) ...[
          AkRow(
            spacing: AkSpacingSize.medium,
            children: [
              Expanded(
                child: AkDropdownInput<String>(
                  label: 'الحالة',
                  value: _statusFilter,
                  items: _statusOptions.map((status) {
                    return DropdownMenuItem<String>(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _statusFilter = value;
                      });
                      _applyFilters(
                          _searchController.text, _statusFilter, _typeFilter);
                    }
                  },
                  prefixIcon: Icons.filter_alt,
                ),
              ),
              Expanded(
                child: AkDropdownInput<String>(
                  label: 'نوع الإجازة',
                  value: _typeFilter,
                  items: ['الكل', ..._leaveTypes].map((type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _typeFilter = value;
                      });
                      _applyFilters(
                          _searchController.text, _statusFilter, _typeFilter);
                    }
                  },
                  prefixIcon: Icons.event,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء جدول الإجازات
  Widget _buildLeavesTable() {
    return AkCard(
      padding: AppDimensions.cardPadding,
      child: DataTableWidget<Leave>(
        columns: [
          DataColumn(
            label: Text(
              'الموظف',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'نوع الإجازة',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'من',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'إلى',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'الأيام',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'الحالة',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
          DataColumn(
            label: Text(
              'الإجراءات',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DynamicColors.textPrimary(context),
                  ),
            ),
          ),
        ],
        items: _filteredLeaves,
        rowBuilder: (leave) {
          return DataRow(
            cells: [
              DataCell(Text(
                leave.employeeName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                    ),
              )),
              DataCell(Text(
                leave.leaveType,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                    ),
              )),
              DataCell(Text(
                DateFormat('yyyy/MM/dd').format(leave.startDate),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                    ),
              )),
              DataCell(Text(
                DateFormat('yyyy/MM/dd').format(leave.endDate),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                    ),
              )),
              DataCell(Text(
                leave.days.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                    ),
              )),
              DataCell(_buildStatusChip(leave.status)),
              DataCell(_buildActionButtons(leave)),
            ],
          );
        },
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'معتمدة':
        color = AppColors.success;
        break;
      case 'مرفوضة':
        color = AppColors.error;
        break;
      case 'قيد المراجعة':
      default:
        color = AppColors.warning;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacing8,
        vertical: AppDimensions.spacing4,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
      ),
      child: Text(
        status,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontSize: AppDimensions.smallFontSize,
              fontWeight: FontWeight.w500,
            ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(Leave leave) {
    return AkRow(
      spacing: AkSpacingSize.tiny,
      children: [
        AkIconButton(
          icon: Icons.edit,
          tooltip: 'تعديل',
          onPressed: () => _setupEditLeaveForm(leave),
          type: AkButtonType.info,
          size: AkButtonSize.small,
        ),
        AkIconButton(
          icon: Icons.delete,
          tooltip: 'حذف',
          onPressed: () => _deleteLeave(leave.id),
          type: AkButtonType.danger,
          size: AkButtonSize.small,
        ),
        AkIconButton(
          icon: Icons.print,
          tooltip: 'طباعة',
          onPressed: () => _printLeave(leave),
          type: AkButtonType.success,
          size: AkButtonSize.small,
        ),
      ],
    );
  }

  /// بناء نموذج الإجازة
  Widget _buildLeaveForm() {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _selectedLeave == null
                            ? 'إضافة إجازة جديدة'
                            : 'تعديل بيانات الإجازة',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _closeForm,
                    ),
                  ],
                ),
                const Divider(),
                const SizedBox(height: AppDimensions.spacing16),
                // حقول النموذج
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف *',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployeeId,
                  items: _employees.map((employee) {
                    return DropdownMenuItem<String>(
                      value: employee.id,
                      child: Text(employee.fullName),
                    );
                  }).toList(),
                  validator: (value) =>
                      value == null ? 'يرجى اختيار الموظف' : null,
                  onChanged: (value) {
                    setState(() {
                      _selectedEmployeeId = value;
                    });
                  },
                ),
                const SizedBox(height: AppDimensions.spacing16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الإجازة *',
                    border: OutlineInputBorder(),
                  ),
                  value: _leaveType,
                  items: _leaveTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  validator: (value) =>
                      value == null ? 'يرجى اختيار نوع الإجازة' : null,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _leaveType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ البداية *',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text: DateFormat('yyyy/MM/dd').format(_startDate),
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate,
                            firstDate: DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );
                          if (date != null) {
                            setState(() {
                              _startDate = date;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ النهاية *',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text: DateFormat('yyyy/MM/dd').format(_endDate),
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate,
                            firstDate: _startDate,
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );
                          if (date != null) {
                            setState(() {
                              _endDate = date;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الحالة',
                    border: OutlineInputBorder(),
                  ),
                  value: _status,
                  items: _statusOptions.map((status) {
                    return DropdownMenuItem<String>(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _status = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: AppDimensions.spacing16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: AppDimensions.spacing24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AkButton(
                      text: _selectedLeave == null
                          ? 'إضافة الإجازة'
                          : 'حفظ التغييرات',
                      icon: Icons.save,
                      onPressed: _saveLeave,
                      type: AkButtonType.primary,
                      size: AkButtonSize.medium,
                    ),
                    const SizedBox(width: AppDimensions.spacing16),
                    AkButton(
                      text: 'إلغاء',
                      icon: Icons.cancel,
                      onPressed: _closeForm,
                      type: AkButtonType.secondary,
                      size: AkButtonSize.medium,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (_isFormSubmitting)
          Container(
            color: Colors.black54,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }
}
