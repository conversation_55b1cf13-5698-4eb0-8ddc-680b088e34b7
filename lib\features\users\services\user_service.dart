import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/auth/services/auth_service.dart';
import '../models/user.dart';
import '../services/role_service.dart';

/// خدمة المستخدمين
class UserService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final RoleService _roleService = RoleService();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers({bool includeInactive = false}) async {
    try {
      final db = await _databaseHelper.database;

      // لا حاجة للتحقق من وجود قاعدة البيانات لأن الدالة database تعيد قاعدة بيانات غير null

      // التحقق من وجود جدول المستخدمين
      final tableExists = await _databaseHelper.tableExists('users');
      if (!tableExists) {
        AppLogger.error(
            'فشل في الحصول على المستخدمين: جدول المستخدمين غير موجود');
        return [];
      }

      // بناء شرط WHERE
      String whereClause = 'u.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND u.is_active = 1';
      }

      try {
        // استعلام مع JOIN للحصول على معلومات الدور ومجموعة المستخدمين والفرع
        final List<Map<String, dynamic>> maps = await db.rawQuery('''
          SELECT
            u.*,
            r.name as role_name,
            r.display_name as role_display_name,
            r.description as role_description,
            ug.name as user_group_name,
            ug.description as user_group_description,
            b.name as branch_name,
            b.address as branch_address,
            b.phone as branch_phone
          FROM users u
          LEFT JOIN roles r ON u.role_id = r.id AND r.is_deleted = 0
          LEFT JOIN user_groups ug ON u.user_group_id = ug.id AND ug.is_deleted = 0
          LEFT JOIN branches b ON u.branch_id = b.id AND b.is_deleted = 0
          WHERE $whereClause
          ORDER BY u.username ASC
        ''', whereArgs);

        AppLogger.info('تم استرجاع ${maps.length} مستخدم من قاعدة البيانات');

        return List.generate(maps.length, (i) {
          final map = maps[i];

          // إضافة معلومات الدور ومجموعة المستخدمين والفرع إلى الخريطة
          if (map['role_id'] != null && map['role_name'] != null) {
            map['role'] = {
              'id': map['role_id'],
              'name': map['role_name'],
              'display_name': map['role_display_name'],
              'description': map['role_description'],
            };
          }

          if (map['user_group_id'] != null && map['user_group_name'] != null) {
            map['user_group'] = {
              'id': map['user_group_id'],
              'name': map['user_group_name'],
              'description': map['user_group_description'],
            };
          }

          if (map['branch_id'] != null && map['branch_name'] != null) {
            map['branch'] = {
              'id': map['branch_id'],
              'name': map['branch_name'],
              'address': map['branch_address'],
              'phone': map['branch_phone'],
            };
          }

          return User.fromMap(map);
        });
      } catch (e) {
        // محاولة استخدام استعلام أبسط في حالة فشل الاستعلام المعقد
        AppLogger.info('ℹ️ تبديل إلى استعلام أبسط (آلية حماية): $e');

        final List<Map<String, dynamic>> maps = await db.query(
          'users',
          where: 'is_deleted = 0',
          orderBy: 'username ASC',
        );

        AppLogger.info(
            'تم استرجاع ${maps.length} مستخدم باستخدام الاستعلام البسيط');

        return maps.map((map) => User.fromMap(map)).toList();
      }
    } catch (e, stackTrace) {
      AppLogger.error('فشل في الحصول على المستخدمين: $e');
      ErrorTracker.captureError(
        'فشل في الحصول على المستخدمين',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(String id) async {
    try {
      final db = await _databaseHelper.database;

      try {
        // استعلام مع JOIN للحصول على معلومات الدور ومجموعة المستخدمين والفرع
        final List<Map<String, dynamic>> maps = await db.rawQuery('''
          SELECT
            u.*,
            r.name as role_name,
            r.display_name as role_display_name,
            r.description as role_description,
            ug.name as user_group_name,
            ug.description as user_group_description,
            b.name as branch_name,
            b.address as branch_address,
            b.phone as branch_phone
          FROM users u
          LEFT JOIN roles r ON u.role_id = r.id AND r.is_deleted = 0
          LEFT JOIN user_groups ug ON u.user_group_id = ug.id AND ug.is_deleted = 0
          LEFT JOIN branches b ON u.branch_id = b.id AND b.is_deleted = 0
          WHERE u.id = ? AND u.is_deleted = 0
          LIMIT 1
        ''', [id]);

        if (maps.isNotEmpty) {
          final map = maps.first;

          // إضافة معلومات الدور ومجموعة المستخدمين والفرع إلى الخريطة
          if (map['role_id'] != null && map['role_name'] != null) {
            map['role'] = {
              'id': map['role_id'],
              'name': map['role_name'],
              'display_name': map['role_display_name'],
              'description': map['role_description'],
            };
          }

          if (map['user_group_id'] != null && map['user_group_name'] != null) {
            map['user_group'] = {
              'id': map['user_group_id'],
              'name': map['user_group_name'],
              'description': map['user_group_description'],
            };
          }

          if (map['branch_id'] != null && map['branch_name'] != null) {
            map['branch'] = {
              'id': map['branch_id'],
              'name': map['branch_name'],
              'address': map['branch_address'],
              'phone': map['branch_phone'],
            };
          }

          return User.fromMap(map);
        }
      } catch (queryError) {
        // محاولة استخدام استعلام أبسط في حالة فشل الاستعلام المعقد
        AppLogger.info('ℹ️ تبديل إلى استعلام أبسط (آلية حماية): $queryError');

        // استعلام بسيط بدون JOIN
        final List<Map<String, dynamic>> simpleMaps = await db.query(
          'users',
          where: 'id = ? AND is_deleted = 0',
          whereArgs: [id],
          limit: 1,
        );

        if (simpleMaps.isNotEmpty) {
          return User.fromMap(simpleMaps.first);
        }
      }

      // إذا لم يتم العثور على المستخدم
      AppLogger.warning('! WARNING: المستخدم غير موجود: $id');
      return null;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في الحصول على المستخدم: $e');
      ErrorTracker.captureError(
        'فشل في الحصول على المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// إضافة مستخدم جديد
  Future<bool> addUser(User user) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
      final List<Map<String, dynamic>> existingUsers = await db.query(
        'users',
        where: 'username = ? AND is_deleted = 0',
        whereArgs: [user.username],
        limit: 1,
      );

      if (existingUsers.isNotEmpty) {
        AppLogger.warning(
            'مستخدم بنفس اسم المستخدم موجود بالفعل: ${user.username}');
        return false;
      }

      // إنشاء معرف جديد إذا لم يكن موجودًا
      final userMap = user.toMap();
      if (userMap['id'] == null) {
        userMap['id'] = const Uuid().v4();
      }

      // تشفير كلمة المرور باستخدام الدالة الموحدة
      if (userMap['password'] != null &&
          userMap['password'].toString().isNotEmpty) {
        userMap['password'] =
            AuthService.hashPassword(userMap['password'].toString());
        AppLogger.info('🔐 تم تشفير كلمة المرور للمستخدم الجديد');
      } else {
        AppLogger.error('❌ كلمة المرور مطلوبة لإنشاء مستخدم جديد');
        throw Exception('كلمة المرور مطلوبة لإنشاء مستخدم جديد');
      }

      // تحويل رمز الدور إلى معرف الدور الفعلي
      if (userMap['role_id'] != null) {
        try {
          final roleCode = userMap['role_id'] as String;

          // التحقق مما إذا كان المعرف هو بالفعل UUID
          final isUuid = roleCode.contains('-') && roleCode.length > 30;

          if (!isUuid) {
            // الحصول على معرف الدور الفعلي من رمز الدور
            final actualRoleId = await _roleService.getRoleIdByCode(roleCode);

            if (actualRoleId != null) {
              // استبدال رمز الدور بمعرف الدور الفعلي
              userMap['role_id'] = actualRoleId;
              AppLogger.info(
                  'تم استبدال رمز الدور "$roleCode" بالمعرف الفعلي: $actualRoleId');

              // لا نحتاج لحفظ اسم الدور - سيتم الحصول عليه عبر JOIN
              AppLogger.info('تم تعيين معرف الدور: $actualRoleId');
            } else {
              AppLogger.error('لم يتم العثور على معرف الدور لرمز: $roleCode');
              throw Exception('لم يتم العثور على معرف الدور لرمز: $roleCode');
            }
          } else {
            // المعرف هو بالفعل UUID - لا نحتاج لحفظ اسم الدور
            AppLogger.info('معرف الدور صحيح: $roleCode');
          }
        } catch (e) {
          AppLogger.error('خطأ في معالجة معرف الدور: $e');
          throw Exception('فشل في معالجة معرف الدور: $e');
        }
      } else {
        AppLogger.warning('لم يتم تحديد معرف الدور للمستخدم');
        throw Exception('لم يتم تحديد معرف الدور للمستخدم');
      }

      // التحقق من صحة معرف مجموعة المستخدمين (بدون حفظ الاسم)
      if (userMap['user_group_id'] != null) {
        try {
          final groupId = userMap['user_group_id'] as String;

          // التحقق من وجود مجموعة المستخدمين
          final List<Map<String, dynamic>> groupData = await db.query(
            'user_groups',
            columns: ['id'],
            where: 'id = ? AND is_deleted = 0',
            whereArgs: [groupId],
            limit: 1,
          );

          if (groupData.isEmpty) {
            AppLogger.warning(
                'لم يتم العثور على مجموعة المستخدمين بالمعرف: $groupId');
            // حذف معرف المجموعة إذا لم يتم العثور عليها
            userMap.remove('user_group_id');
          } else {
            AppLogger.info('تم التحقق من صحة معرف مجموعة المستخدمين: $groupId');
          }
        } catch (e) {
          AppLogger.error('خطأ في معالجة معرف مجموعة المستخدمين: $e');
          // حذف معرف المجموعة في حالة حدوث خطأ
          userMap.remove('user_group_id');
        }
      }

      // التحقق من صحة معرف الفرع (بدون حفظ الاسم)
      if (userMap['branch_id'] != null) {
        try {
          final branchId = userMap['branch_id'] as String;

          // التحقق من وجود الفرع
          final List<Map<String, dynamic>> branchData = await db.query(
            'branches',
            columns: ['id'],
            where: 'id = ? AND is_deleted = 0',
            whereArgs: [branchId],
            limit: 1,
          );

          if (branchData.isEmpty) {
            AppLogger.warning('لم يتم العثور على الفرع بالمعرف: $branchId');
            // حذف معرف الفرع إذا لم يتم العثور عليه
            userMap.remove('branch_id');
          } else {
            AppLogger.info('تم التحقق من صحة معرف الفرع: $branchId');
          }
        } catch (e) {
          AppLogger.error('خطأ في التحقق من معرف الفرع: $e');
          // حذف معرف الفرع في حالة حدوث خطأ
          userMap.remove('branch_id');
        }
      }

      // طباعة بيانات المستخدم للتصحيح
      AppLogger.info('بيانات المستخدم قبل الإدراج: ${userMap.toString()}');

      // إدراج المستخدم في قاعدة البيانات
      await db.insert('users', userMap);
      AppLogger.info('تم إدراج المستخدم بنجاح');

      // تهيئة الأدوار والصلاحيات التلقائية للمستخدم الجديد
      await _initializeUserPermissions(userMap['id'], userMap['role_id']);

      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في إضافة مستخدم: $e');
      ErrorTracker.captureError(
        'فشل في إضافة مستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'user': user.toString()},
      );
      return false;
    }
  }

  /// تحديث مستخدم موجود
  Future<bool> updateUser(User user) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المستخدم
      final existingUser = await getUserById(user.id);
      if (existingUser == null) {
        AppLogger.warning('المستخدم غير موجود: ${user.id}');
        return false;
      }

      // التحقق مما إذا كان المستخدم هو المستخدم الأساسي
      final isSystemAdmin = await _isSystemAdminUser(user.id);
      final isFirstUser = await _isFirstUserInSystem(user.id);

      // إذا كان المستخدم هو المستخدم الأساسي، نقوم بحماية بعض الخصائص الحساسة
      if (isSystemAdmin || isFirstUser) {
        AppLogger.info('🔒 تم اكتشاف محاولة تحديث المستخدم الأساسي');

        // التأكد من أن المستخدم الأساسي يظل نشطًا دائمًا
        if (user.isActive == false) {
          AppLogger.warning(
              '⚠️ محاولة تعطيل المستخدم الأساسي - تم تجاهل هذا التغيير');
          user = user.copyWith(isActive: true);
        }

        // التأكد من أن المستخدم الأساسي لا يتم حذفه
        if (user.isDeleted) {
          AppLogger.warning(
              '⚠️ محاولة حذف المستخدم الأساسي - تم تجاهل هذا التغيير');
          user = user.copyWith(isDeleted: false);
        }

        // الاحتفاظ بدور المستخدم الأساسي إذا كان دورًا أساسيًا
        if (existingUser.roleName != null &&
            (existingUser.roleName!.toLowerCase().contains('مالك') ||
                existingUser.roleName!.toLowerCase().contains('مدير') ||
                existingUser.roleName!.toLowerCase().contains('admin') ||
                existingUser.roleName!.toLowerCase().contains('owner'))) {
          // إذا كان المستخدم يحاول تغيير الدور إلى دور غير أساسي
          if (user.roleName != null &&
              !(user.roleName!.toLowerCase().contains('مالك') ||
                  user.roleName!.toLowerCase().contains('مدير') ||
                  user.roleName!.toLowerCase().contains('admin') ||
                  user.roleName!.toLowerCase().contains('owner'))) {
            AppLogger.warning(
                '⚠️ محاولة تغيير دور المستخدم الأساسي إلى دور غير أساسي - تم تجاهل هذا التغيير');
            user = user.copyWith(roleId: existingUser.roleId);
          }
        }
      }

      // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
      final List<Map<String, dynamic>> existingUsers = await db.query(
        'users',
        where: 'username = ? AND id != ? AND is_deleted = 0',
        whereArgs: [user.username, user.id],
        limit: 1,
      );

      if (existingUsers.isNotEmpty) {
        AppLogger.warning(
            'مستخدم بنفس اسم المستخدم موجود بالفعل: ${user.username}');
        return false;
      }

      final updatedMap = user.toMap();
      updatedMap['updated_at'] = DateTime.now().toIso8601String();

      // تشفير كلمة المرور إذا تم تحديثها
      if (updatedMap['password'] != null &&
          updatedMap['password'].toString().isNotEmpty &&
          updatedMap['password'] != existingUser.password) {
        updatedMap['password'] =
            AuthService.hashPassword(updatedMap['password'].toString());
        AppLogger.info('🔐 تم تشفير كلمة المرور المحدثة للمستخدم');
      } else if (updatedMap['password'] == null ||
          updatedMap['password'].toString().isEmpty) {
        // إذا لم يتم تحديد كلمة مرور جديدة، احتفظ بالقديمة
        updatedMap['password'] = existingUser.password;
        AppLogger.info('🔐 تم الاحتفاظ بكلمة المرور الحالية');
      }

      // تحويل رمز الدور إلى معرف الدور الفعلي
      if (updatedMap['role_id'] != null) {
        try {
          final roleCode = updatedMap['role_id'] as String;

          // التحقق مما إذا كان المعرف هو بالفعل UUID
          final isUuid = roleCode.contains('-') && roleCode.length > 30;

          if (!isUuid) {
            // الحصول على معرف الدور الفعلي من رمز الدور
            final actualRoleId = await _roleService.getRoleIdByCode(roleCode);

            if (actualRoleId != null) {
              // استبدال رمز الدور بمعرف الدور الفعلي
              updatedMap['role_id'] = actualRoleId;
              AppLogger.info(
                  'تم استبدال رمز الدور "$roleCode" بالمعرف الفعلي: $actualRoleId');

              // لا نحتاج لحفظ اسم الدور - سيتم الحصول عليه عبر JOIN
              AppLogger.info('تم تعيين معرف الدور: $actualRoleId');
            } else {
              AppLogger.error('لم يتم العثور على معرف الدور لرمز: $roleCode');
              throw Exception('لم يتم العثور على معرف الدور لرمز: $roleCode');
            }
          } else {
            // المعرف هو بالفعل UUID - لا نحتاج لحفظ اسم الدور
            AppLogger.info('معرف الدور صحيح: $roleCode');
          }
        } catch (e) {
          AppLogger.error('خطأ في معالجة معرف الدور: $e');
          throw Exception('فشل في معالجة معرف الدور: $e');
        }
      }

      // التحقق من صحة معرف مجموعة المستخدمين (بدون حفظ الاسم)
      if (updatedMap['user_group_id'] != null) {
        try {
          final groupId = updatedMap['user_group_id'] as String;

          // التحقق من وجود مجموعة المستخدمين
          final List<Map<String, dynamic>> groupData = await db.query(
            'user_groups',
            columns: ['id'],
            where: 'id = ? AND is_deleted = 0',
            whereArgs: [groupId],
            limit: 1,
          );

          if (groupData.isEmpty) {
            AppLogger.warning(
                'لم يتم العثور على مجموعة المستخدمين بالمعرف: $groupId');
            // حذف معرف المجموعة إذا لم يتم العثور عليها
            updatedMap.remove('user_group_id');
          } else {
            AppLogger.info('تم التحقق من صحة معرف مجموعة المستخدمين: $groupId');
          }
        } catch (e) {
          AppLogger.error('خطأ في معالجة معرف مجموعة المستخدمين: $e');
          // حذف معرف المجموعة في حالة حدوث خطأ
          updatedMap.remove('user_group_id');
        }
      }

      // التأكد من أن المستخدم الأساسي يظل نشطًا وغير محذوف
      if (isSystemAdmin || isFirstUser) {
        updatedMap['is_active'] = 1;
        updatedMap['is_deleted'] = 0;
      }

      await db.update(
        'users',
        updatedMap,
        where: 'id = ?',
        whereArgs: [user.id],
      );

      AppLogger.info('✅ تم تحديث المستخدم بنجاح: ${user.username}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تحديث مستخدم: $e');
      ErrorTracker.captureError(
        'فشل في تحديث مستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'user': user.toString()},
      );
      return false;
    }
  }

  /// حذف مستخدم (حذف منطقي)
  Future<bool> deleteUser(String id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من أن المستخدم ليس المستخدم الأساسي (مدير النظام)
      final user = await getUserById(id);
      if (user == null) {
        AppLogger.warning('محاولة حذف مستخدم غير موجود: $id');
        return false;
      }

      // التحقق من أن المستخدم ليس المستخدم الأساسي
      final isAdminUser = await _isSystemAdminUser(id);
      if (isAdminUser) {
        AppLogger.error('⛔ محاولة حذف المستخدم الأساسي غير مسموحة');

        // إضافة حماية إضافية: إعادة تفعيل المستخدم الأساسي إذا كان معطلاً
        if (user.isActive == false) {
          await db.update(
            'users',
            {
              'is_active': 1,
              'is_deleted': 0,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [id],
          );
          AppLogger.info('🔒 تم إعادة تفعيل المستخدم الأساسي تلقائياً');
        }

        // حفظ سجل محاولة الحذف في ملف خاص
        _logUnauthorizedDeletionAttempt(id, user.username);

        return false;
      }

      // التحقق مرة أخرى باستخدام طريقة مختلفة (حماية مزدوجة)
      final isFirstUser = await _isFirstUserInSystem(id);
      if (isFirstUser) {
        AppLogger.error('⛔ محاولة حذف المستخدم الأول في النظام غير مسموحة');
        return false;
      }

      // التحقق من أن المستخدم ليس المستخدم الوحيد المتبقي
      final allUsers = await getAllUsers(includeInactive: true);
      if (allUsers.length <= 1) {
        AppLogger.error('⛔ لا يمكن حذف المستخدم الوحيد المتبقي في النظام');
        return false;
      }

      await db.update(
        'users',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info('✅ تم حذف المستخدم بنجاح: $id');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في حذف مستخدم: $e');
      ErrorTracker.captureError(
        'فشل في حذف مستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// التحقق مما إذا كان المستخدم هو أول مستخدم في النظام
  Future<bool> _isFirstUserInSystem(String userId) async {
    try {
      final db = await _databaseHelper.database;

      // طريقة 1: التحقق باستخدام rowid
      final firstUserByRowId =
          await db.rawQuery('SELECT id FROM users ORDER BY rowid ASC LIMIT 1');

      if (firstUserByRowId.isNotEmpty) {
        final firstId = firstUserByRowId.first['id'] as String;
        if (firstId == userId) {
          return true;
        }
      }

      // طريقة 2: التحقق باستخدام تاريخ الإنشاء
      final firstUserByDate = await db.query(
        'users',
        orderBy: 'created_at ASC',
        limit: 1,
      );

      if (firstUserByDate.isNotEmpty) {
        final firstId = firstUserByDate.first['id'] as String;
        if (firstId == userId) {
          return true;
        }
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من المستخدم الأول: $e');
      return false;
    }
  }

  /// تسجيل محاولة حذف غير مصرح بها
  void _logUnauthorizedDeletionAttempt(String userId, String username) {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logMessage =
          'محاولة حذف غير مصرح بها للمستخدم الأساسي: $username (ID: $userId) في $timestamp';
      AppLogger.error(logMessage);

      // يمكن إضافة المزيد من الإجراءات هنا مثل إرسال إشعار للمسؤول
    } catch (e) {
      AppLogger.error('خطأ في تسجيل محاولة الحذف: $e');
    }
  }

  /// التحقق مما إذا كان المستخدم هو المستخدم الأساسي الذي لا يمكن حذفه أبدًا
  Future<bool> _isSystemAdminUser(String userId) async {
    try {
      final db = await _databaseHelper.database;

      // الطريقة 1: التحقق من أن المستخدم هو أول مستخدم تم إنشاؤه في النظام
      // هذه الطريقة تعتمد على ترتيب تاريخ الإنشاء
      final firstUserByCreationDate = await db.query(
        'users',
        orderBy: 'created_at ASC',
        limit: 1,
      );

      if (firstUserByCreationDate.isNotEmpty) {
        final firstUserId = firstUserByCreationDate.first['id'] as String;
        if (firstUserId == userId) {
          AppLogger.info(
              '🔒 المستخدم $userId هو المستخدم الأساسي (أول مستخدم تم إنشاؤه)');
          return true;
        }
      }

      // الطريقة 2: التحقق من أن المستخدم له أقل معرف في النظام
      // هذه الطريقة تعتمد على ترتيب المعرفات
      final firstUserById = await db.query(
        'users',
        orderBy: 'id ASC',
        limit: 1,
      );

      if (firstUserById.isNotEmpty) {
        final firstId = firstUserById.first['id'] as String;
        if (firstId == userId) {
          AppLogger.info('🔒 المستخدم $userId هو المستخدم الأساسي (أقل معرف)');
          return true;
        }
      }

      // الطريقة 3: التحقق من أن المستخدم هو أول مستخدم في الجدول
      // هذه الطريقة تعتمد على ترتيب الإدخال في الجدول
      final firstUserByRowId =
          await db.rawQuery('SELECT id FROM users ORDER BY rowid ASC LIMIT 1');

      if (firstUserByRowId.isNotEmpty) {
        final firstRowId = firstUserByRowId.first['id'] as String;
        if (firstRowId == userId) {
          AppLogger.info(
              '🔒 المستخدم $userId هو المستخدم الأساسي (أول صف في الجدول)');
          return true;
        }
      }

      // الطريقة 4: التحقق من وجود علامة خاصة في اسم المستخدم أو البريد الإلكتروني
      // هذه الطريقة تعتمد على وجود علامة خاصة في بيانات المستخدم
      final user = await getUserById(userId);
      if (user != null) {
        if (user.username.toLowerCase().contains('admin') ||
            user.username.toLowerCase().contains('مدير') ||
            user.username.toLowerCase().contains('مالك') ||
            user.username.toLowerCase().contains('owner') ||
            (user.email != null &&
                user.email!.toLowerCase().contains('admin'))) {
          AppLogger.info(
              '🔒 المستخدم $userId هو المستخدم الأساسي (بناءً على الاسم أو البريد)');
          return true;
        }
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من المستخدم الأساسي: $e');
      return false;
    }
  }

  /// تغيير حالة نشاط المستخدم
  Future<bool> toggleUserStatus(String id, bool isActive) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المستخدم
      final user = await getUserById(id);
      if (user == null) {
        AppLogger.warning('محاولة تغيير حالة مستخدم غير موجود: $id');
        return false;
      }

      // إذا كانت العملية هي تعطيل المستخدم (isActive = false)
      if (!isActive) {
        // التحقق من أن المستخدم ليس المستخدم الأساسي (طريقة 1)
        final isAdminUser = await _isSystemAdminUser(id);
        if (isAdminUser) {
          AppLogger.error('⛔ محاولة تعطيل المستخدم الأساسي غير مسموحة');

          // حفظ سجل محاولة التعطيل
          _logUnauthorizedDeactivationAttempt(id, user.username);

          // إعادة تفعيل المستخدم الأساسي إذا كان معطلاً بالفعل
          if (user.isActive == false) {
            await db.update(
              'users',
              {
                'is_active': 1,
                'updated_at': DateTime.now().toIso8601String(),
              },
              where: 'id = ?',
              whereArgs: [id],
            );
            AppLogger.info('🔒 تم إعادة تفعيل المستخدم الأساسي تلقائياً');
          }

          return false;
        }

        // التحقق من أن المستخدم ليس المستخدم الأساسي (طريقة 2)
        final isFirstUser = await _isFirstUserInSystem(id);
        if (isFirstUser) {
          AppLogger.error('⛔ محاولة تعطيل المستخدم الأول في النظام غير مسموحة');
          return false;
        }

        // التحقق من أن المستخدم ليس المستخدم النشط الوحيد المتبقي
        final activeUsers = await getAllUsers(includeInactive: false);
        if (activeUsers.length <= 1 && activeUsers.any((u) => u.id == id)) {
          AppLogger.error(
              '⛔ لا يمكن تعطيل المستخدم النشط الوحيد المتبقي في النظام');
          return false;
        }
      }

      // تنفيذ التغيير
      await db.update(
        'users',
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info(
          '✅ تم ${isActive ? 'تفعيل' : 'تعطيل'} المستخدم بنجاح: $id');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تغيير حالة المستخدم: $e');
      ErrorTracker.captureError(
        'فشل في تغيير حالة مستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id, 'isActive': isActive},
      );
      return false;
    }
  }

  /// تسجيل محاولة تعطيل غير مصرح بها
  void _logUnauthorizedDeactivationAttempt(String userId, String username) {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logMessage =
          'محاولة تعطيل غير مصرح بها للمستخدم الأساسي: $username (ID: $userId) في $timestamp';
      AppLogger.error(logMessage);

      // يمكن إضافة المزيد من الإجراءات هنا مثل إرسال إشعار للمسؤول
    } catch (e) {
      AppLogger.error('خطأ في تسجيل محاولة التعطيل: $e');
    }
  }

  /// تعيين مجموعة المستخدم
  Future<bool> setUserGroup(String userId, String userGroupId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المستخدم
      final existingUser = await getUserById(userId);
      if (existingUser == null) {
        AppLogger.warning('المستخدم غير موجود: $userId');
        return false;
      }

      // التحقق من وجود مجموعة المستخدمين
      final List<Map<String, dynamic>> existingGroups = await db.query(
        'user_groups',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [userGroupId],
        limit: 1,
      );

      if (existingGroups.isEmpty) {
        AppLogger.warning('مجموعة المستخدمين غير موجودة: $userGroupId');
        return false;
      }

      // تحديث مجموعة المستخدم (فقط المعرف - بدون اسم مكرر)
      await db.update(
        'users',
        {
          'user_group_id': userGroupId,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تعيين مجموعة المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId, 'userGroupId': userGroupId},
      );
      return false;
    }
  }

  /// تعيين دور المستخدم
  Future<bool> setUserRole(String userId, String roleId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المستخدم
      final existingUser = await getUserById(userId);
      if (existingUser == null) {
        AppLogger.warning('المستخدم غير موجود: $userId');
        return false;
      }

      // التحقق من وجود الدور
      final List<Map<String, dynamic>> existingRoles = await db.query(
        'roles',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [roleId],
        limit: 1,
      );

      if (existingRoles.isEmpty) {
        AppLogger.warning('الدور غير موجود: $roleId');
        return false;
      }

      // تحديث دور المستخدم (فقط المعرف - بدون اسم مكرر)
      await db.update(
        'users',
        {
          'role_id': roleId,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      AppLogger.info('تم تعيين دور المستخدم بنجاح: $userId -> $roleId');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تعيين دور المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {'userId': userId, 'roleId': roleId},
      );
      return false;
    }
  }

  /// تهيئة الأدوار والصلاحيات التلقائية للمستخدم الجديد
  Future<void> _initializeUserPermissions(String userId, String? roleId) async {
    try {
      if (roleId == null) {
        AppLogger.warning('لا يمكن تهيئة الصلاحيات بدون معرف دور');
        return;
      }

      AppLogger.info('🔄 بدء تهيئة الأدوار والصلاحيات للمستخدم: $userId');

      // الحصول على معلومات الدور
      final db = await _databaseHelper.database;
      final roleData = await db.query(
        'roles',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [roleId],
        limit: 1,
      );

      if (roleData.isNotEmpty) {
        final roleName = roleData.first['name'] as String?;
        AppLogger.info('✅ تم ربط المستخدم بالدور: $roleName');

        // يمكن إضافة المزيد من منطق تهيئة الصلاحيات هنا حسب الحاجة
        // مثل إنشاء إعدادات افتراضية للمستخدم، أو تعيين صلاحيات خاصة

        AppLogger.info('✅ تم تهيئة الأدوار والصلاحيات بنجاح للمستخدم: $userId');
      } else {
        AppLogger.warning('⚠️ لم يتم العثور على الدور: $roleId');
      }
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة الأدوار والصلاحيات: $e');
      // لا نرمي استثناء هنا لأن إنشاء المستخدم نجح، فقط التهيئة فشلت
    }
  }
}
