import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';

/// رأس لوحة المعلومات مع معلومات المستخدم والتاريخ
class DashboardHeader extends StatelessWidget {
  /// اسم المستخدم
  final String? userName;

  /// صورة المستخدم
  final String? userImage;

  /// عنوان الترحيب
  final String? welcomeTitle;

  /// نص الترحيب
  final String? welcomeMessage;

  /// دالة عند النقر على صورة المستخدم
  final VoidCallback? onUserTap;

  /// دالة لفتح القائمة الجانبية
  final VoidCallback? onOpenDrawer;

  /// دالة عند النقر على زر البحث
  final VoidCallback? onSearchTap;

  /// دالة عند النقر على زر الإشعارات
  final VoidCallback? onNotificationTap;

  /// عدد الإشعارات غير المقروءة
  final int unreadNotifications;

  /// ما إذا كان يعرض التاريخ
  final bool showDate;

  /// ما إذا كان يعرض الوقت
  final bool showTime;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color? textColor;

  const DashboardHeader({
    Key? key,
    this.userName,
    this.userImage,
    this.welcomeTitle,
    this.welcomeMessage,
    this.onUserTap,
    this.onOpenDrawer,
    this.onSearchTap,
    this.onNotificationTap,
    this.unreadNotifications = 0,
    this.showDate = true,
    this.showTime = true,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final dateFormat = DateFormat('EEEE, d MMMM yyyy', 'ar');
    final timeFormat = DateFormat('h:mm a', 'ar');

    final formattedDate = dateFormat.format(now);
    final formattedTime = timeFormat.format(now);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final defaultTextColor = textColor ?? theme.textTheme.bodyLarge?.color;
    // textColor ?? Theme.of(context).textTheme.bodyLarge?.color;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: backgroundColor != null
            ? [
                BoxShadow(
                  color: AppColors.getAdaptiveShadowColor(isDark),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // معلومات المستخدم
              Expanded(
                child: Row(
                  children: [
                    if (userImage != null)
                      GestureDetector(
                        onTap: onOpenDrawer ?? onUserTap,
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: DynamicColors.primary,
                              width: 2,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(24),
                            child: Image.network(
                              userImage!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return CircleAvatar(
                                  backgroundColor: DynamicColors.primary
                                      .withValues(alpha: 0.2),
                                  child: Icon(
                                    Icons.person,
                                    color: DynamicColors.primary,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      )
                    else
                      GestureDetector(
                        onTap: onOpenDrawer ?? onUserTap,
                        child: const CircleAvatar(
                          radius: 24,
                          // DynamicColors.primary.withValues(alpha: 0.2),
                          child: Icon(
                            Icons.person,
                            //color: AppColors.primary,
                            //color: DynamicColors.primary,
                          ),
                        ),
                      ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            welcomeTitle ?? 'مرحباً',
                            /*   style: AppTypography(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              // color: DynamicColors.primary,
                            ), */
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                              color: DynamicColors.primaryDynamic(context),
                              fontSize: Layout.getResponsiveFontSize(20),
                            ),
                          ),
                          Text(
                            welcomeMessage ?? userName ?? 'مستخدم النظام',
                            style: AppTypography(
                              fontSize: 14,
                              // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                              color: DynamicColors.primaryDynamic(context)
                                  .withValues(alpha: 0.7),
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              Row(
                children: [
                  if (onSearchTap != null)
                    IconButton(
                      onPressed: onSearchTap,
                      icon: Icon(
                        Icons.search,
                        // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                        color: DynamicColors.primaryDynamic(context),
                      ),
                      tooltip: 'بحث',
                    ),
                  if (onNotificationTap != null)
                    Stack(
                      children: [
                        IconButton(
                          onPressed: onNotificationTap,
                          icon: Icon(
                            Icons.notifications_outlined,
                            // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                            color: DynamicColors.primaryDynamic(context),
                          ),
                          tooltip: 'الإشعارات',
                        ),
                        if (unreadNotifications > 0)
                          Positioned(
                            right: 8,
                            top: 8,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: DynamicColors.primaryDynamic(context),
                                shape: BoxShape.circle,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                unreadNotifications > 9
                                    ? '9+'
                                    : unreadNotifications.toString(),
                                style: const AppTypography(
                                  color: AppColors.onPrimary,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                ],
              ),
            ],
          ),

          // التاريخ والوقت
          if (showDate || showTime)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (showDate)
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                          color: DynamicColors.primaryDynamic(context)
                              .withValues(alpha: 0.9),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          formattedDate,
                          style: AppTypography(
                            fontSize: 12,
                            color: defaultTextColor?.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  if (showTime)
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                          color: DynamicColors.primaryDynamic(context)
                              .withValues(alpha: 0.7),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          formattedTime,
                          style: AppTypography(
                            fontSize: 12,
                            color: defaultTextColor?.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
