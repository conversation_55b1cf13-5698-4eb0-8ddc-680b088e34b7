import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import '../services/session_manager.dart';
import 'database_schema.dart';
import 'basic_data_initializer.dart';

// ملاحظة: تم حذف ملفي database_initializer.dart و database_tables_manager.dart
// وتم دمج وظائفهما الأساسية في هذا الملف

/// Helper class for database operations
class DatabaseHelper {
  // Singleton pattern
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  // متغير للاختبارات فقط
  static Database? testDatabase;

  // متغير لتتبع ما إذا كانت عملية تهيئة قاعدة البيانات قيد التنفيذ
  bool _isInitializing = false;

  /// Get the database instance
  /// يستخدم نمط Lazy Initialization لتأجيل تهيئة قاعدة البيانات حتى الحاجة إليها
  /// لا يقوم بتهيئة قاعدة البيانات في شاشة البداية، بل يؤجلها إلى شاشة الإعداد الأولي
  Future<Database> get database async {
    // للاختبارات: إذا كان هناك قاعدة بيانات اختبار، استخدمها
    if (testDatabase != null) return testDatabase!;

    // التحقق مما إذا كانت قاعدة البيانات مفتوحة بالفعل
    if (_database != null && _database!.isOpen) {
      return _database!;
    }

    // منع تهيئة قاعدة البيانات مرة أخرى إذا كانت قيد التهيئة بالفعل
    if (_isInitializing) {
      // انتظار حتى تكتمل عملية التهيئة
      while (_isInitializing && _database == null) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (_database != null) {
        return _database!;
      }
    }

    _isInitializing = true;

    try {
      // تهيئة قاعدة البيانات عند الحاجة إليها
      _database = await _initDatabase();
      return _database!;
    } finally {
      _isInitializing = false;
    }
  }

  /// الحصول على مسار قاعدة البيانات
  Future<String> getDatabasePath() async {
    try {
      // الحصول على مسار قاعدة البيانات في مكان أكثر سهولة للوصول إليه
      final Directory externalDir = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();
      final String dbFolderPath = join(externalDir.path, 'tajer_plus_db');
      final String path = join(dbFolderPath, AppConstants.dbName);

      return path;
    } catch (e) {
      AppLogger.error('فشل في الحصول على مسار قاعدة البيانات: $e');
      // استخدام مسار افتراضي في حالة الفشل
      final docsDir = await getApplicationDocumentsDirectory();
      return join(docsDir.path, AppConstants.dbName);
    }
  }

  /// التحقق من وجود ملف قاعدة البيانات فقط
  /// تستخدم هذه الدالة في شاشة البداية للتحقق من وجود قاعدة البيانات
  /// دون فتحها أو تهيئتها، مما يجعل شاشة البداية خفيفة جدًا
  Future<bool> databaseFileExists() async {
    try {
      final dbPath = await getDatabasePath();
      final dbFile = File(dbPath);
      final exists = await dbFile.exists();

      AppLogger.info(
          'التحقق من وجود ملف قاعدة البيانات: ${exists ? "موجود" : "غير موجود"}');
      return exists;
    } catch (e) {
      AppLogger.error('فشل في التحقق من وجود ملف قاعدة البيانات: $e');
      return false;
    }
  }

  /// للتوافق مع الكود القديم
  Future<bool> databaseExists() async {
    return databaseFileExists();
  }

  /// فتح قاعدة البيانات الموجودة فقط دون تهيئة
  /// تستخدم هذه الدالة في شاشة البداية لفتح قاعدة البيانات الموجودة
  /// دون إنشاء جداول أو بيانات أساسية
  Future<Database?> openExistingDatabase() async {
    try {
      // التحقق من وجود ملف قاعدة البيانات
      final dbExists = await databaseFileExists();
      if (!dbExists) {
        AppLogger.warning('ملف قاعدة البيانات غير موجود، لا يمكن فتحه');
        return null;
      }

      // إذا كانت قاعدة البيانات مفتوحة بالفعل، نعيدها
      if (_database != null && _database!.isOpen) {
        return _database;
      }

      // الحصول على مسار قاعدة البيانات
      final dbPath = await getDatabasePath();

      AppLogger.info('فتح قاعدة البيانات الموجودة دون تهيئة...');

      // فتح قاعدة البيانات بدون onCreate (نمرر null بدلاً من دالة onCreate)
      final db = await openDatabase(
        dbPath,
        version: AppConstants.dbVersion,
        onOpen: _onOpenWithoutInitialization,
        readOnly: false,
        singleInstance: true,
      );

      _database = db;

      // تطبيق إعدادات PRAGMA
      await _applyPragmaSettings(db);

      AppLogger.info('تم فتح قاعدة البيانات الموجودة بنجاح');
      return db;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في فتح قاعدة البيانات الموجودة: $e');
      ErrorTracker.captureError(
        'فشل في فتح قاعدة البيانات الموجودة',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إنشاء قاعدة البيانات والجداول
  /// تستخدم هذه الدالة في شاشة الإعداد الأولي لإنشاء قاعدة البيانات والجداول
  Future<Database?> createDatabaseAndTables() async {
    try {
      AppLogger.info('إنشاء قاعدة البيانات والجداول...');

      // التأكد من إغلاق قاعدة البيانات إذا كانت مفتوحة
      await closeDatabase();

      // الحصول على مسار قاعدة البيانات
      final dbPath = await getDatabasePath();

      // التأكد من وجود المجلد
      final dbDirectory = Directory(dirname(dbPath));
      if (!await dbDirectory.exists()) {
        await dbDirectory.create(recursive: true);
      }

      // فتح قاعدة البيانات مع onCreate
      final db = await openDatabase(
        dbPath,
        version: AppConstants.dbVersion,
        onCreate: _onCreateTablesOnly,
        onOpen: _onOpenWithoutInitialization,
        readOnly: false,
        singleInstance: true,
      );

      _database = db;

      // تطبيق إعدادات PRAGMA
      await _applyPragmaSettings(db);

      AppLogger.info('تم إنشاء قاعدة البيانات والجداول بنجاح');
      return db;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في إنشاء قاعدة البيانات والجداول: $e');
      ErrorTracker.captureError(
        'فشل في إنشاء قاعدة البيانات والجداول',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// تهيئة البيانات الأساسية
  /// تستخدم هذه الدالة في شاشة الإعداد الأولي لتهيئة البيانات الأساسية
  Future<bool> initializeBasicData() async {
    try {
      AppLogger.info('تهيئة البيانات الأساسية...');

      // التحقق من وجود قاعدة البيانات
      if (_database == null || !_database!.isOpen) {
        AppLogger.error(
            'قاعدة البيانات غير مفتوحة، لا يمكن تهيئة البيانات الأساسية');
        return false;
      }

      // التحقق من وجود البيانات الأساسية
      final hasBasicData =
          await BasicDataInitializer.checkBasicDataExists(_database!);
      if (hasBasicData) {
        AppLogger.info('البيانات الأساسية موجودة بالفعل');
        return true;
      }

      // تهيئة البيانات الأساسية
      try {
        // استخدام مُهيئ البيانات الأساسية
        await BasicDataInitializer.initialize(_database!);
        AppLogger.info('✅ تم تهيئة البيانات الأساسية بنجاح');
      } catch (e) {
        AppLogger.error('❌ فشل في تهيئة البيانات الأساسية: $e');
        rethrow;
      }

      // التحقق مرة أخرى
      final dataInitialized =
          await BasicDataInitializer.checkBasicDataExists(_database!);
      if (!dataInitialized) {
        AppLogger.error('فشل في تهيئة البيانات الأساسية');
        return false;
      }

      AppLogger.info('تم تهيئة البيانات الأساسية بنجاح');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تهيئة البيانات الأساسية: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة البيانات الأساسية',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Initialize the database
  /// تهيئة قاعدة البيانات مع تطبيق إعدادات PRAGMA المناسبة
  /// تم تحسين هذه الدالة لتكون أكثر كفاءة وأمانًا
  Future<Database> _initDatabase() async {
    try {
      AppLogger.info('Initializing database...');

      // Get the database path in a more accessible location
      final Directory externalDir = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();
      final String dbFolderPath = join(externalDir.path, 'tajer_plus_db');
      final String path = join(dbFolderPath, AppConstants.dbName);

      AppLogger.info('Database path: $path');

      // Ensure directory exists
      final dbDirectory = Directory(dbFolderPath);
      if (!await dbDirectory.exists()) {
        await dbDirectory.create(recursive: true);
      }

      // Check if database file exists
      final dbFile = File(path);
      final dbExists = await dbFile.exists();

      if (!dbExists) {
        AppLogger.info(
            'Database file does not exist, will create new database');
      } else {
        AppLogger.info(
            'Database file exists, size: ${await dbFile.length()} bytes');
      }

      // فتح قاعدة البيانات مع الخيارات المحددة
      AppLogger.info('فتح قاعدة البيانات مع تعطيل وضع WAL...');

      // استخدام try-catch لمعالجة خطأ "database is locked"
      Database? db;
      int retryCount = 0;
      const maxRetries = 3;

      while (db == null && retryCount < maxRetries) {
        try {
          db = await openDatabase(
            path,
            version: AppConstants.dbVersion,
            onCreate: _onCreate,
            onUpgrade: _onUpgrade,
            onDowngrade: _onDowngrade,
            onOpen: _onOpen,
            // تعطيل وضع WAL تماماً عند فتح قاعدة البيانات
            readOnly: false,
            singleInstance: true,
          );
        } catch (e) {
          retryCount++;
          AppLogger.warning(
              '❌ فشل في فتح قاعدة البيانات (محاولة $retryCount): $e');

          if (e.toString().contains('database is locked')) {
            // محاولة إغلاق أي اتصالات مفتوحة
            AppLogger.info('محاولة إغلاق اتصالات قاعدة البيانات المفتوحة...');
            await _closeDatabase();

            // انتظار قبل إعادة المحاولة
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
          } else if (retryCount >= maxRetries) {
            rethrow;
          }
        }
      }

      if (db == null) {
        throw Exception('فشل في فتح قاعدة البيانات بعد $maxRetries محاولات');
      }

      // تطبيق إعدادات PRAGMA مباشرة بعد فتح قاعدة البيانات وقبل أي معاملة
      try {
        AppLogger.info('تطبيق إعدادات PRAGMA مباشرة بعد فتح قاعدة البيانات...');

        // استخدام وضع DELETE بدلاً من WAL لتجنب مشاكل التوافق
        // يجب تنفيذ هذا الأمر أولاً وخارج أي معاملة
        final journalMode = await db.rawQuery('PRAGMA journal_mode = DELETE');
        AppLogger.info('✅ تم ضبط وضع السجل: $journalMode');

        // تفعيل المفاتيح الخارجية - خارج أي معاملة
        await db.execute('PRAGMA foreign_keys = ON');
        AppLogger.info('✅ تم تفعيل المفاتيح الخارجية');

        // تحسين الأداء بتغيير وضع المزامنة
        await db.execute('PRAGMA synchronous = NORMAL');
        AppLogger.info('✅ تم ضبط وضع المزامنة');

        // زيادة حجم التخزين المؤقت لتحسين الأداء
        await db.execute('PRAGMA cache_size = 2000');
        AppLogger.info('✅ تم ضبط حجم التخزين المؤقت');

        // تخزين الجداول المؤقتة في الذاكرة
        await db.execute('PRAGMA temp_store = MEMORY');
        AppLogger.info('✅ تم ضبط وضع تخزين الجداول المؤقتة');

        // التحقق من سلامة قاعدة البيانات
        final integrityCheck = await db.rawQuery('PRAGMA quick_check');
        AppLogger.info('✅ نتيجة فحص سلامة قاعدة البيانات: $integrityCheck');

        AppLogger.info('✅ تم تطبيق جميع إعدادات PRAGMA بنجاح');
      } catch (e, stackTrace) {
        AppLogger.error('❌ فشل في تطبيق إعدادات PRAGMA: $e');
        AppLogger.error('❌ تفاصيل الخطأ: $stackTrace');
      }

      return db;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to initialize database',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// إغلاق قاعدة البيانات
  /// تستخدم هذه الدالة لإغلاق اتصال قاعدة البيانات بشكل آمن
  /// ملاحظة: يجب استخدام هذه الدالة فقط عند الضرورة القصوى مثل حالات الخطأ
  /// أو عند إغلاق التطبيق، لأن إغلاق قاعدة البيانات وإعادة فتحها يستهلك موارد كثيرة
  Future<void> _closeDatabase() async {
    if (_database != null && _database!.isOpen) {
      try {
        AppLogger.info(
            '⚠️ جاري إغلاق قاعدة البيانات (يجب استخدام هذه الدالة فقط عند الضرورة)');
        await _database!.close();
        _database = null;
        AppLogger.info('✅ تم إغلاق قاعدة البيانات بنجاح');
      } catch (e) {
        AppLogger.error('❌ فشل في إغلاق قاعدة البيانات: $e');
      }
    }
  }

  /// إغلاق قاعدة البيانات عند إغلاق التطبيق
  /// يجب استدعاء هذه الدالة فقط عند إغلاق التطبيق
  Future<void> closeDatabase() async {
    await _closeDatabase();
  }

  /// إنشاء جداول قاعدة البيانات فقط دون تهيئة البيانات الأساسية
  /// تستخدم هذه الدالة في شاشة الإعداد الأولي لإنشاء الجداول فقط
  Future<void> _onCreateTablesOnly(Database db, int version) async {
    try {
      AppLogger.info('إنشاء جداول قاعدة البيانات للإصدار $version...');

      // إنشاء جميع الجداول في معاملة واحدة باستخدام المخطط الموحد
      await db.transaction((txn) async {
        // استخدام المخطط الموحد لإنشاء جميع الجداول
        await DatabaseSchema.createAllTables(txn);
      });

      AppLogger.info('تم إنشاء جداول قاعدة البيانات بنجاح');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء جداول قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'version': version},
      );
      rethrow;
    }
  }

  /// Create the database tables
  Future<void> _onCreate(Database db, int version) async {
    try {
      AppLogger.info('Creating database tables for version $version...');

      // Create all tables in a transaction using the unified schema
      await db.transaction((txn) async {
        // استخدام المخطط الموحد لإنشاء جميع الجداول
        await DatabaseSchema.createAllTables(txn);
      });

      AppLogger.info('Database tables created successfully');

      // تهيئة البيانات الأساسية بعد إنشاء الجداول
      // التحقق من وجود البيانات الأساسية أولاً
      final hasBasicData = await BasicDataInitializer.checkBasicDataExists(db);
      if (!hasBasicData) {
        AppLogger.info('البيانات الأساسية غير موجودة، سيتم تهيئتها...');
        try {
          // استخدام مُهيئ البيانات الأساسية
          await BasicDataInitializer.initialize(db);
          AppLogger.info('✅ تم تهيئة البيانات الأساسية بنجاح');
        } catch (e) {
          AppLogger.error('❌ فشل في تهيئة البيانات الأساسية: $e');
          rethrow;
        }
      } else {
        AppLogger.info('البيانات الأساسية موجودة بالفعل');
      }

      // تهيئة قاعدة البيانات بعد إنشائها
      await _initializeDatabaseAfterCreation(db);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to create database tables',
        error: e,
        stackTrace: stackTrace,
        context: {'version': version},
      );
      rethrow;
    }
  }

  /// تهيئة قاعدة البيانات بعد إنشائها
  Future<void> _initializeDatabaseAfterCreation(Database db) async {
    try {
      AppLogger.info('Initializing database after creation...');

      // يمكن إضافة أي عمليات تهيئة إضافية هنا
      // مثل إنشاء بيانات افتراضية أو إعدادات النظام

      AppLogger.info('Database initialized successfully after creation');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to initialize database after creation',
        error: e,
        stackTrace: stackTrace,
      );
      // لا نقوم بإعادة رمي الاستثناء هنا لتجنب فشل إنشاء قاعدة البيانات
      AppLogger.error('Error initializing database after creation: $e');
    }
  }

  /// Upgrade the database
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      AppLogger.info(
          'Upgrading database from version $oldVersion to $newVersion...');

      // Apply migrations based on version
      await db.transaction((txn) async {
        // تم حذف استدعاء _tablesManager.applyMigrations
        // يمكن إضافة منطق الترحيل هنا عند الحاجة
        await _applyMigrations(txn, oldVersion, newVersion);
      });

      AppLogger.info('Database upgraded successfully');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to upgrade database',
        error: e,
        stackTrace: stackTrace,
        context: {'oldVersion': oldVersion, 'newVersion': newVersion},
      );
      rethrow;
    }
  }

  /// Downgrade the database
  Future<void> _onDowngrade(Database db, int oldVersion, int newVersion) async {
    try {
      AppLogger.info(
          'Downgrading database from version $oldVersion to $newVersion...');

      // Apply reverse migrations
      await db.transaction((txn) async {
        // تم حذف استدعاء _tablesManager.applyReverseMigrations
        // يمكن إضافة منطق الترحيل العكسي هنا عند الحاجة
        await _applyReverseMigrations(txn, oldVersion, newVersion);
      });

      AppLogger.info('Database downgraded successfully');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to downgrade database',
        error: e,
        stackTrace: stackTrace,
        context: {'oldVersion': oldVersion, 'newVersion': newVersion},
      );
      rethrow;
    }
  }

  /// تطبيق إعدادات PRAGMA
  Future<void> _applyPragmaSettings(Database db) async {
    try {
      AppLogger.info('تطبيق إعدادات PRAGMA...');

      // استخدام وضع DELETE بدلاً من WAL لتجنب مشاكل التوافق
      final journalMode = await db.rawQuery('PRAGMA journal_mode = DELETE');
      AppLogger.info('✅ تم ضبط وضع السجل: $journalMode');

      // تفعيل المفاتيح الخارجية
      await db.execute('PRAGMA foreign_keys = ON');
      AppLogger.info('✅ تم تفعيل المفاتيح الخارجية');

      // تحسين الأداء بتغيير وضع المزامنة
      await db.execute('PRAGMA synchronous = NORMAL');
      AppLogger.info('✅ تم ضبط وضع المزامنة');

      // زيادة حجم التخزين المؤقت لتحسين الأداء
      await db.execute('PRAGMA cache_size = 4000');
      AppLogger.info('✅ تم ضبط حجم التخزين المؤقت');

      // تخزين الجداول المؤقتة في الذاكرة
      await db.execute('PRAGMA temp_store = MEMORY');
      AppLogger.info('✅ تم ضبط وضع تخزين الجداول المؤقتة');

      // تمكين تحسين الاستعلامات
      await db.execute('PRAGMA query_only = OFF');
      AppLogger.info('✅ تم تمكين تحسين الاستعلامات');

      // تحسين أداء الفهارس
      await db.execute('PRAGMA automatic_index = ON');
      AppLogger.info('✅ تم تمكين الفهارس التلقائية');

      // تحسين أداء الذاكرة المؤقتة - تم تعطيل هذا الإعداد لأنه يسبب مشاكل في بعض إصدارات Android
      // await db.execute('PRAGMA mmap_size = 30000000');
      // AppLogger.info('✅ تم ضبط حجم الذاكرة المؤقتة');

      // تحسين أداء القراءة والكتابة
      await db.execute('PRAGMA page_size = 4096');
      AppLogger.info('✅ تم ضبط حجم الصفحة');

      // تحسين أداء المعاملات - تم تعطيله لأنه يسبب خطأ في بعض إصدارات Android
      // await db.execute('PRAGMA busy_timeout = 5000');
      // AppLogger.info('✅ تم ضبط مهلة الانتظار');

      // التحقق من هيكل الجداول وتحديثها إذا لزم الأمر
      await _verifyAndUpdateTableStructures(db);

      // التحقق من سلامة قاعدة البيانات
      final integrityCheck = await db.rawQuery('PRAGMA quick_check');
      AppLogger.info('✅ نتيجة فحص سلامة قاعدة البيانات: $integrityCheck');

      AppLogger.info('✅ تم تطبيق جميع إعدادات PRAGMA بنجاح');
    } catch (e) {
      AppLogger.error('❌ فشل في تطبيق إعدادات PRAGMA: $e');
    }
  }

  /// التحقق من وجود عمود في جدول
  Future<bool> _columnExists(Database db, String table, String column) async {
    try {
      final result = await db.rawQuery("PRAGMA table_info($table)");
      return result.any((row) => row['name'] == column);
    } catch (e) {
      AppLogger.error(
          'خطأ في التحقق من وجود العمود $column في الجدول $table: $e');
      return false;
    }
  }

  /// التحقق من هيكل الجداول وتحديثها إذا لزم الأمر
  Future<void> _verifyAndUpdateTableStructures(Database db) async {
    try {
      AppLogger.info('التحقق من هيكل الجداول وتحديثها إذا لزم الأمر...');

      // التحقق من جدول المستخدمين
      final usersTableExists = await tableExists('users');
      if (usersTableExists) {
        AppLogger.info('جدول المستخدمين موجود، التحقق من الأعمدة...');

        // التحقق من وجود عمود avatar
        final avatarExists = await _columnExists(db, 'users', 'avatar');
        if (!avatarExists) {
          AppLogger.info('إضافة عمود avatar إلى جدول المستخدمين...');
          await db.execute('ALTER TABLE users ADD COLUMN avatar TEXT');
        }

        // التحقق من وجود عمود role_name
        final roleNameExists = await _columnExists(db, 'users', 'role_name');
        if (!roleNameExists) {
          AppLogger.info('إضافة عمود role_name إلى جدول المستخدمين...');
          await db.execute('ALTER TABLE users ADD COLUMN role_name TEXT');

          // تحديث قيم role_name من جدول الأدوار
          await db.execute('''
            UPDATE users SET role_name = (
              SELECT name FROM roles WHERE roles.id = users.role_id
            ) WHERE role_id IS NOT NULL
          ''');
        }

        // التحقق من وجود عمود user_group_name
        final userGroupNameExists =
            await _columnExists(db, 'users', 'user_group_name');
        if (!userGroupNameExists) {
          AppLogger.info('إضافة عمود user_group_name إلى جدول المستخدمين...');
          await db.execute('ALTER TABLE users ADD COLUMN user_group_name TEXT');

          // تحديث قيم user_group_name من جدول مجموعات المستخدمين
          await db.execute('''
            UPDATE users SET user_group_name = (
              SELECT name FROM user_groups WHERE user_groups.id = users.user_group_id
            ) WHERE user_group_id IS NOT NULL
          ''');
        }

        // التحقق من وجود عمود branch_id
        final branchIdExists = await _columnExists(db, 'users', 'branch_id');
        if (!branchIdExists) {
          AppLogger.info('إضافة عمود branch_id إلى جدول المستخدمين...');
          await db.execute('ALTER TABLE users ADD COLUMN branch_id TEXT');
        }

        // ملاحظة: تم إزالة إضافة عمود branch_name لتجنب تكرار البيانات
        // سيتم الحصول على اسم الفرع عبر JOIN مع جدول branches
      }

      AppLogger.info('✅ تم التحقق من هيكل الجداول وتحديثها بنجاح');
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في التحقق من هيكل الجداول وتحديثها: $e');
      ErrorTracker.captureError(
        'فشل في التحقق من هيكل الجداول وتحديثها',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// دالة فتح قاعدة البيانات بدون تهيئة
  Future<void> _onOpenWithoutInitialization(Database db) async {
    try {
      AppLogger.info('تم فتح قاعدة البيانات بنجاح');

      // التحقق من سلامة قاعدة البيانات
      final result = await db.rawQuery('PRAGMA integrity_check');
      AppLogger.info('نتيجة فحص سلامة قاعدة البيانات: $result');

      // التحقق من عدد الجداول
      final tableCount = await db.rawQuery(
          "SELECT count(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
      final count = Sqflite.firstIntValue(tableCount) ?? 0;
      AppLogger.info('قاعدة البيانات تحتوي على $count جدول');
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في فتح قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'خطأ في فتح قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Called when the database is opened
  Future<void> _onOpen(Database db) async {
    try {
      AppLogger.info('Database opened successfully');

      // ملاحظة: تم نقل أوامر PRAGMA إلى دالة _initDatabase
      // لتنفيذها مباشرة بعد فتح قاعدة البيانات وقبل أي معاملة

      // Check database integrity
      final result = await db.rawQuery('PRAGMA integrity_check');
      AppLogger.info('Database integrity check: $result');

      // Check if database is empty
      final tableCount = await db.rawQuery(
          "SELECT count(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
      final count = Sqflite.firstIntValue(tableCount) ?? 0;
      AppLogger.info('Database contains $count tables');

      if (count < 5) {
        AppLogger.warning(
            'Database appears to be missing tables, may need initialization');

        // إنشاء الجداول المفقودة مباشرة
        await _initializeDatabase();

        // فقط التحقق من وجود البيانات الأساسية للتسجيل
        final hasBasicData =
            await BasicDataInitializer.checkBasicDataExists(db);

        if (!hasBasicData) {
          AppLogger.warning(
              '⚠️ البيانات الأساسية غير موجودة، سيتم توجيه المستخدم إلى شاشة الإعداد الأولي');
          // لا نقوم بتهيئة البيانات الأساسية هنا، بل نترك ذلك لشاشة الإعداد الأولي
        } else {
          AppLogger.info('✅ البيانات الأساسية موجودة بالفعل');
        }
      } else {
        // التحقق من صحة قاعدة البيانات
        await checkDatabaseHealth();
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error in database onOpen',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // متغير لتتبع ما إذا كانت عملية التحقق من صحة قاعدة البيانات قيد التنفيذ
  bool _isCheckingHealth = false;

  /// Check database health and repair if needed
  Future<bool> checkDatabaseHealth() async {
    // منع الحلقة اللانهائية
    if (_isCheckingHealth) {
      AppLogger.info(
          'عملية التحقق من صحة قاعدة البيانات قيد التنفيذ بالفعل، تم تجاوزها');
      return true; // إرجاع قيمة افتراضية لتجنب الحلقة اللانهائية
    }

    _isCheckingHealth = true;

    try {
      AppLogger.info('Checking database health...');

      // Get the database
      final db = await database;

      // Check integrity
      final integrityCheck = await db.rawQuery('PRAGMA integrity_check');
      final isIntegrityOk = integrityCheck.isNotEmpty &&
          integrityCheck.first.containsKey('integrity_check') &&
          integrityCheck.first['integrity_check'] == 'ok';

      if (!isIntegrityOk) {
        AppLogger.warning('Database integrity check failed: $integrityCheck');
        _isCheckingHealth = false; // إعادة تعيين المتغير قبل استدعاء دالة أخرى
        return await repairDatabase();
      }

      // تم إيقاف التحقق من وجود الجداول الأساسية مؤقتًا
      // سيتم التعامل مع إنشاء الجداول بشكل كامل في DatabaseInitializer

      AppLogger.info('Database integrity check passed');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to check database health',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    } finally {
      _isCheckingHealth =
          false; // إعادة تعيين المتغير في النهاية لضمان إعادة تعيينه حتى في حالة حدوث خطأ
    }
  }

  // تم تعليق هذه الدالة لأنها غير مستخدمة
  /*
  /// التحقق من تطابق هيكل جميع الجداول الرئيسية
  /// تم تعطيل هذه الدالة مؤقتًا حتى يتم تحديد هيكل الجداول بشكل نهائي
  Future<bool> _verifyAllTableStructures() async {
    // تم تعطيل التحقق من هيكل الجداول مؤقتًا
    AppLogger.info('تم تعطيل التحقق من هيكل الجداول مؤقتًا');
    return true;
  }
  */

  /// Repair the database
  /// إصلاح قاعدة البيانات في حالة وجود مشاكل
  /// تم تحسين هذه الدالة لتكون أكثر أمانًا وفعالية
  Future<bool> repairDatabase() async {
    try {
      AppLogger.info('🔧 بدء عملية إصلاح قاعدة البيانات...');

      // Close the database if it's open
      await _closeDatabase();

      // Get the database path in a more accessible location
      final Directory externalDir = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();
      final String dbFolderPath = join(externalDir.path, 'tajer_plus_db');
      final String path = join(dbFolderPath, AppConstants.dbName);

      // Create backup with timestamp before deleting
      final dbFile = File(path);
      if (await dbFile.exists()) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final backupPath = '$path.backup.$timestamp';
        try {
          await dbFile.copy(backupPath);
          AppLogger.info(
              '✅ تم إنشاء نسخة احتياطية من قاعدة البيانات في: $backupPath');
        } catch (e) {
          AppLogger.warning(
              '⚠️ فشل في إنشاء نسخة احتياطية من قاعدة البيانات: $e');
        }

        // محاولة إصلاح قاعدة البيانات قبل حذفها
        try {
          AppLogger.info('🔧 محاولة إصلاح قاعدة البيانات باستخدام VACUUM...');
          final tempDb = await openDatabase(path, readOnly: false);
          await tempDb.execute('VACUUM');
          await tempDb.close();
          AppLogger.info('✅ تم تنفيذ VACUUM بنجاح');
        } catch (e) {
          AppLogger.warning('⚠️ فشل في تنفيذ VACUUM: $e');
        }

        // حذف ملف قاعدة البيانات فقط إذا فشلت محاولات الإصلاح
        try {
          await dbFile.delete();
          AppLogger.info('✅ تم حذف ملف قاعدة البيانات');
        } catch (e) {
          AppLogger.error('❌ فشل في حذف ملف قاعدة البيانات: $e');
          return false;
        }
      }

      // Delete WAL files if they exist
      try {
        final walFile = File('$path-wal');
        if (await walFile.exists()) {
          await walFile.delete();
          AppLogger.info('✅ تم حذف ملف WAL');
        }

        final shmFile = File('$path-shm');
        if (await shmFile.exists()) {
          await shmFile.delete();
          AppLogger.info('✅ تم حذف ملف SHM');
        }
      } catch (e) {
        AppLogger.warning('⚠️ فشل في حذف ملفات WAL/SHM: $e');
        // نستمر في العملية حتى لو فشل حذف هذه الملفات
      }

      // Reinitialize the database
      try {
        AppLogger.info('🔄 إعادة تهيئة قاعدة البيانات...');
        _database = await _initDatabase();

        // Verify database was created successfully
        final db = _database!;

        // إنشاء الجداول بدون معاملة لتجنب مشاكل PRAGMA
        AppLogger.info('🔄 إنشاء الجداول بعد الإصلاح...');
        await DatabaseSchema.createAllTables(db);

        AppLogger.info('✅ تم إنشاء الجداول بنجاح');

        // فقط التحقق من وجود البيانات الأساسية بعد إصلاح قاعدة البيانات
        final hasBasicData =
            await BasicDataInitializer.checkBasicDataExists(db);
        if (!hasBasicData) {
          AppLogger.warning(
              '⚠️ البيانات الأساسية غير موجودة بعد الإصلاح، سيتم توجيه المستخدم إلى شاشة الإعداد الأولي');
          // لا نقوم بتهيئة البيانات الأساسية هنا، بل نترك ذلك لشاشة الإعداد الأولي

          // إعادة تعيين حالة الإعداد والتشغيل الأول لتوجيه المستخدم إلى شاشة الإعداد الأولي
          await SessionManager.setSetupCompleted(false);
          await SessionManager.setFirstLaunch(true);
        } else {
          AppLogger.info('✅ البيانات الأساسية موجودة بالفعل بعد الإصلاح');
        }

        AppLogger.info('✅ تم إصلاح قاعدة البيانات بنجاح');
        return true;
      } catch (e, stackTrace) {
        AppLogger.error('❌ فشل في إعادة تهيئة قاعدة البيانات: $e');
        ErrorTracker.captureError(
          'فشل في إعادة تهيئة قاعدة البيانات بعد الإصلاح',
          error: e,
          stackTrace: stackTrace,
        );

        // إعادة تعيين حالة الإعداد والتشغيل الأول لتوجيه المستخدم إلى شاشة الإعداد الأولي
        await SessionManager.setSetupCompleted(false);
        await SessionManager.setFirstLaunch(true);

        return false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل عام في إصلاح قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'فشل عام في إصلاح قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );

      // إعادة تعيين حالة الإعداد والتشغيل الأول لتوجيه المستخدم إلى شاشة الإعداد الأولي
      await SessionManager.setSetupCompleted(false);
      await SessionManager.setFirstLaunch(true);

      return false;
    }
  }

  /// التحقق من وجود جدول
  /// يستخدم للتحقق من وجود جدول في قاعدة البيانات
  Future<bool> tableExists(String tableName) async {
    try {
      final db = await database;
      final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]);
      return result.isNotEmpty;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في التحقق من وجود جدول',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName},
      );
      return false;
    }
  }

  // تم نقل هذه الدالة إلى أعلى الملف

  /// التحقق من وجود جدول في معاملة
  /// يستخدم للتحقق من وجود جدول في معاملة قاعدة البيانات
  static Future<bool> tableExistsInTransaction(
      Transaction txn, String tableName) async {
    try {
      final result = await txn.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]);
      return result.isNotEmpty;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في التحقق من وجود جدول في معاملة',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName},
      );
      return false;
    }
  }

  /// التحقق من تطابق هيكل الجدول
  /// يستخدم للتحقق من تطابق هيكل الجدول مع الهيكل المتوقع
  Future<bool> verifyTableStructure(
      String tableName, List<String> expectedColumns) async {
    try {
      AppLogger.info('التحقق من هيكل جدول $tableName...');

      final db = await database;

      // التحقق من وجود الجدول
      final tableExists = await this.tableExists(tableName);
      if (!tableExists) {
        AppLogger.warning('الجدول $tableName غير موجود');
        return false;
      }

      // الحصول على معلومات الجدول
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');

      // استخراج أسماء الأعمدة
      final actualColumns =
          tableInfo.map((column) => column['name'] as String).toList();

      // التحقق من وجود جميع الأعمدة المتوقعة
      for (final column in expectedColumns) {
        if (!actualColumns.contains(column)) {
          AppLogger.warning('العمود $column غير موجود في جدول $tableName');
          return false;
        }
      }

      AppLogger.info('هيكل جدول $tableName مطابق للهيكل المتوقع');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في التحقق من هيكل جدول $tableName',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'expectedColumns': expectedColumns},
      );
      return false;
    }
  }

  /// إضافة عمود إلى جدول
  /// يستخدم لإضافة عمود جديد إلى جدول موجود
  Future<bool> addColumnToTable(
      String tableName, String columnName, String columnType) async {
    try {
      AppLogger.info('إضافة عمود $columnName إلى جدول $tableName...');

      final db = await database;

      // التحقق من وجود الجدول
      final tableExists = await this.tableExists(tableName);
      if (!tableExists) {
        AppLogger.warning('الجدول $tableName غير موجود');
        return false;
      }

      // التحقق من وجود العمود
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');
      final columnExists =
          tableInfo.any((column) => column['name'] == columnName);

      if (columnExists) {
        AppLogger.info('العمود $columnName موجود بالفعل في جدول $tableName');
        return true;
      }

      // إضافة العمود
      await db
          .execute('ALTER TABLE $tableName ADD COLUMN $columnName $columnType');

      AppLogger.info('تمت إضافة عمود $columnName إلى جدول $tableName بنجاح');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة عمود $columnName إلى جدول $tableName',
        error: e,
        stackTrace: stackTrace,
        context: {
          'tableName': tableName,
          'columnName': columnName,
          'columnType': columnType
        },
      );
      return false;
    }
  }

  /// تهيئة قاعدة البيانات وإنشاء الجداول
  Future<void> _initializeDatabase() async {
    try {
      AppLogger.info('تهيئة قاعدة البيانات...');
      final db = await database;

      // إنشاء جميع الجداول
      await DatabaseSchema.createAllTables(db);

      AppLogger.info('تم تهيئة قاعدة البيانات بنجاح');
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تهيئة قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// تطبيق عمليات الترحيل بين الإصدارات
  Future<void> _applyMigrations(
      Transaction txn, int oldVersion, int newVersion) async {
    // يمكن إضافة منطق الترحيل هنا عند الحاجة
    // مثال:
    if (oldVersion < 2 && newVersion >= 2) {
      await _migrateToV2(txn);
    }

    if (oldVersion < 3 && newVersion >= 3) {
      await _migrateToV3(txn);
    }
  }

  /// ترحيل قاعدة البيانات إلى الإصدار 2
  Future<void> _migrateToV2(Transaction txn) async {
    AppLogger.info('ترحيل قاعدة البيانات إلى الإصدار 2...');
    // لا نحتاج لأي منطق ترحيل في هذه المرحلة من التطوير
    // العمود branch_access_type موجود بالفعل في المخطط
  }

  /// ترحيل قاعدة البيانات إلى الإصدار 3
  Future<void> _migrateToV3(Transaction txn) async {
    AppLogger.info('ترحيل قاعدة البيانات إلى الإصدار 3...');
    // لا نحتاج لأي منطق ترحيل في هذه المرحلة من التطوير
  }

  /// تطبيق عمليات الترحيل العكسي
  Future<void> _applyReverseMigrations(
      Transaction txn, int oldVersion, int newVersion) async {
    // يمكن إضافة منطق الترحيل العكسي هنا عند الحاجة
    AppLogger.info('تطبيق عمليات الترحيل العكسي...');
  }
}
