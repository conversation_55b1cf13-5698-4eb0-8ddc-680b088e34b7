import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tajer_plus/core/utils/index.dart';

import '../../../core/widgets/index.dart';
import 'package:tajer_plus/core/models/payroll.dart';
import 'package:tajer_plus/core/models/payroll_status.dart';
import 'package:tajer_plus/features/payrolls/screens/payroll_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة عرض قائمة الرواتب
class PayrollsScreen extends StatefulWidget {
  const PayrollsScreen({Key? key}) : super(key: key);

  @override
  State<PayrollsScreen> createState() => _PayrollsScreenState();
}

class _PayrollsScreenState extends State<PayrollsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Payroll> _payrolls = [];
  List<Payroll> _filteredPayrolls = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPayrolls();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الرواتب
  Future<void> _loadPayrolls() async {
    // هنا سيتم استدعاء خدمة جلب بيانات الرواتب
    // مثال:
    // final payrollService = PayrollService();
    // final payrolls = await payrollService.getPayrolls();

    // بيانات تجريبية للعرض
    await Future.delayed(const Duration(seconds: 1));
    final now = DateTime.now();
    final dummyPayrolls = [
      Payroll(
        id: '1',
        payrollId: 'PAY-2023-01',
        title: 'رواتب شهر ${now.month - 1}/${now.year}',
        payPeriodStart: DateTime(now.year, now.month - 1, 1),
        payPeriodEnd: DateTime(now.year, now.month - 1, 30),
        paymentDate: DateTime(now.year, now.month, 5),
        status: PayrollStatus.paid,
        branchName: 'الفرع الرئيسي',
        metadata: {'userName': 'أحمد محمد'},
      ),
      Payroll(
        id: '2',
        payrollId: 'PAY-2023-02',
        title: 'رواتب شهر ${now.month}/${now.year}',
        payPeriodStart: DateTime(now.year, now.month, 1),
        payPeriodEnd: DateTime(now.year, now.month, 30),
        paymentDate: DateTime(now.year, now.month + 1, 5),
        status: PayrollStatus.approved,
        branchName: 'الفرع الرئيسي',
        metadata: {'userName': 'أحمد محمد'},
      ),
      Payroll(
        id: '3',
        payrollId: 'PAY-2023-03',
        title: 'رواتب شهر ${now.month + 1}/${now.year}',
        payPeriodStart: DateTime(now.year, now.month + 1, 1),
        payPeriodEnd: DateTime(now.year, now.month + 1, 30),
        paymentDate: DateTime(now.year, now.month + 2, 5),
        status: PayrollStatus.draft,
        branchName: 'فرع المدينة',
        metadata: {'userName': 'سارة أحمد'},
      ),
    ];

    setState(() {
      _payrolls = dummyPayrolls;
      _filteredPayrolls = dummyPayrolls;
      _isLoading = false;
    });
  }

  /// البحث في قائمة الرواتب
  void _searchPayrolls(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredPayrolls = _payrolls;
      });
      return;
    }

    final filtered = _payrolls.where((payroll) {
      final payrollNumber = payroll.payrollId.toLowerCase();
      final branchName = payroll.branchName?.toLowerCase() ?? '';
      final userName =
          payroll.metadata?['userName']?.toString().toLowerCase() ?? '';
      final searchLower = query.toLowerCase();

      return payrollNumber.contains(searchLower) ||
          branchName.contains(searchLower) ||
          userName.contains(searchLower);
    }).toList();

    setState(() {
      _filteredPayrolls = filtered;
    });
  }

  /// حذف راتب
  Future<void> _deletePayroll(String id) async {
    // هنا سيتم استدعاء خدمة حذف الراتب
    // مثال:
    // final payrollService = PayrollService();
    // await payrollService.deletePayroll(id);

    setState(() {
      _payrolls.removeWhere((payroll) => payroll.id == id);
      _filteredPayrolls.removeWhere((payroll) => payroll.id == id);
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حذف الراتب بنجاح')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الرواتب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadPayrolls();
            },
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PayrollFormScreen(),
            ),
          ).then((_) => _loadPayrolls());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          _buildSearchAndFilter(),
          const SizedBox(height: AppDimensions.spacing16),
          Expanded(
            child: _buildPayrollsTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'بحث عن راتب...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: _searchPayrolls,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PayrollFormScreen(),
              ),
            ).then((_) => _loadPayrolls());
          },
          icon: const Icon(Icons.add),
          label: const Text('إضافة راتب'),
        ),
      ],
    );
  }

  Widget _buildPayrollsTable() {
    if (_filteredPayrolls.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد رواتب',
          style: AppTypography(fontSize: 18),
        ),
      );
    }

    return Layout.isDesktop() || Layout.isTablet()
        ? _buildDesktopTable()
        : _buildMobileList();
  }

  Widget _buildDesktopTable() {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return DataTableWidget<Payroll>(
      columns: const [
        DataColumn(label: Text('رقم الراتب')),
        DataColumn(label: Text('الفترة')),
        DataColumn(label: Text('تاريخ الدفع')),
        DataColumn(label: Text('الفرع')),
        DataColumn(label: Text('المستخدم')),
        DataColumn(label: Text('الحالة')),
        DataColumn(label: Text('إجراءات')),
      ],
      items: _filteredPayrolls,
      rowBuilder: (payroll) {
        return DataRow(
          cells: [
            DataCell(Text(payroll.payrollId)),
            DataCell(Text(
              '${dateFormat.format(payroll.payPeriodStart)} - ${dateFormat.format(payroll.payPeriodEnd)}',
            )),
            DataCell(Text(dateFormat.format(payroll.paymentDate))),
            DataCell(Text(payroll.branchName ?? '')),
            DataCell(Text(payroll.metadata?['userName'] ?? '')),
            DataCell(_buildStatusChip(payroll.status)),
            DataCell(_buildActionButtons(payroll)),
          ],
        );
      },
    );
  }

  Widget _buildMobileList() {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return ListView.builder(
      itemCount: _filteredPayrolls.length,
      itemBuilder: (context, index) {
        final payroll = _filteredPayrolls[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(
              payroll.payrollId,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'الفترة: ${dateFormat.format(payroll.payPeriodStart)} - ${dateFormat.format(payroll.payPeriodEnd)}'),
                Text('تاريخ الدفع: ${dateFormat.format(payroll.paymentDate)}'),
                Text('الفرع: ${payroll.branchName ?? ''}'),
                Text('المستخدم: ${payroll.metadata?['userName'] ?? ''}'),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusChip(payroll.status),
                IconButton(
                  icon: const Icon(Icons.edit, color: AppColors.info),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            PayrollFormScreen(payroll: payroll),
                      ),
                    ).then((_) => _loadPayrolls());
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  onPressed: () => _showDeleteConfirmation(payroll),
                ),
              ],
            ),
            isThreeLine: true,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PayrollFormScreen(payroll: payroll),
                ),
              ).then((_) => _loadPayrolls());
            },
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(PayrollStatus status) {
    Color color;
    String text;

    switch (status) {
      case PayrollStatus.draft:
        color = AppColors.secondary;
        text = 'مسودة';
        break;
      case PayrollStatus.approved:
        color = AppColors.info;
        text = 'معتمد';
        break;
      case PayrollStatus.paid:
        color = AppColors.success;
        text = 'مدفوع';
        break;
      case PayrollStatus.cancelled:
        color = AppColors.error;
        text = 'ملغي';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const AppTypography(color: AppColors.onPrimary, fontSize: 12),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.all(4),
    );
  }

  Widget _buildActionButtons(Payroll payroll) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.edit, color: AppColors.info),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PayrollFormScreen(payroll: payroll),
              ),
            ).then((_) => _loadPayrolls());
          },
        ),
        IconButton(
          icon: const Icon(Icons.delete, color: AppColors.error),
          onPressed: () => _showDeleteConfirmation(payroll),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(Payroll payroll) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الراتب ${payroll.payrollId}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deletePayroll(payroll.id);
            },
            child:
                const Text('حذف', style: AppTypography(color: AppColors.error)),
          ),
        ],
      ),
    );
  }
}
