import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import '../utils/error_tracker.dart';
import '../utils/app_logger.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// خدمة لإدارة عمليات الصور
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  final ImagePicker _picker = ImagePicker();
  final Uuid _uuid = const Uuid();

  /// التقاط صورة من الكاميرا
  Future<File?> captureImage({
    double? maxWidth = 800,
    double? maxHeight = 800,
    int quality = 85,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );

      if (pickedFile == null) return null;
      return File(pickedFile.path);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al capturar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// اختيار صورة من المعرض
  Future<File?> pickImage({
    double? maxWidth = 800,
    double? maxHeight = 800,
    int quality = 85,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );

      if (pickedFile == null) return null;
      return File(pickedFile.path);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al seleccionar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// ضغط الصورة وتحويلها إلى تنسيق WebP مع تحسين متقدم
  Future<File?> compressAndConvertToWebP(
    File imageFile, {
    int quality = 85,
    int minWidth = 800,
    int minHeight = 800,
  }) async {
    try {
      // الحصول على الدليل المؤقت
      final tempDir = await getTemporaryDirectory();
      final targetPath = path.join(
        tempDir.path,
        '${_uuid.v4()}.webp',
      );

      // الحصول على معلومات الصورة الأصلية
      final originalFileSize = await imageFile.length();
      AppLogger.info('حجم الصورة الأصلي: ${_formatFileSize(originalFileSize)}');

      // ضغط وتحويل إلى WebP
      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        targetPath,
        quality: quality,
        minWidth: minWidth,
        minHeight: minHeight,
        format: CompressFormat.webp,
      );

      if (result == null) {
        throw Exception('خطأ في ضغط الصورة');
      }

      // التحقق من الحجم بعد الضغط
      final compressedFile = File(result.path);
      final compressedFileSize = await compressedFile.length();
      final compressionRatio = originalFileSize / compressedFileSize;

      AppLogger.info('الحجم بعد الضغط: ${_formatFileSize(compressedFileSize)}');
      AppLogger.info('نسبة الضغط: ${compressionRatio.toStringAsFixed(2)}x');

      // Si la compresión no fue efectiva, intentar con calidad más baja
      if (compressionRatio < 1.5 && quality > 60) {
        AppLogger.info(
            'La compresión no fue suficiente, intentando con calidad más baja');

        // Eliminar el archivo comprimido anterior
        await compressedFile.delete();

        // Intentar con calidad más baja
        return compressAndConvertToWebP(
          imageFile,
          quality: quality - 15,
          minWidth: minWidth,
          minHeight: minHeight,
        );
      }

      return compressedFile;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al comprimir imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Formatear tamaño de archivo para mostrar en logs
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  }

  /// حفظ الصورة محليًا مع تحسين متقدم
  Future<String?> saveImageLocally(
    File imageFile,
    String folderPath, {
    bool compressBeforeUpload = true,
    int quality = 85,
    int maxWidth = 1200,
    int maxHeight = 1200,
  }) async {
    try {
      AppLogger.info('بدء عملية حفظ الصورة محليًا');
      File fileToUpload = imageFile;

      // ضغط الصورة قبل الحفظ إذا لزم الأمر
      if (compressBeforeUpload) {
        AppLogger.info('جاري ضغط الصورة قبل الحفظ...');

        // التحقق من نوع الملف
        final extension = path.extension(imageFile.path).toLowerCase();
        AppLogger.info('نوع الملف الأصلي: $extension');

        // ضغط وتحويل إلى WebP للتحسين
        final compressedFile = await compressAndConvertToWebP(
          imageFile,
          quality: quality,
          minWidth: maxWidth,
          minHeight: maxHeight,
        );

        if (compressedFile != null) {
          fileToUpload = compressedFile;
          AppLogger.info('تم ضغط الصورة بنجاح');
        } else {
          AppLogger.warning('تعذر ضغط الصورة، سيتم استخدام الصورة الأصلية');
        }
      }

      // إنشاء اسم فريد للصورة
      final fileName = '${_uuid.v4()}.webp';

      // الحصول على مسار مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final targetDir = Directory('${appDir.path}/$folderPath');

      // إنشاء المجلد إذا لم يكن موجودًا
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final targetPath = '${targetDir.path}/$fileName';
      AppLogger.info('مسار الحفظ: $targetPath');

      // نسخ الملف إلى المسار المستهدف
      await fileToUpload.copy(targetPath);
      AppLogger.info('تم حفظ الصورة بنجاح');

      return targetPath;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حفظ الصورة محليًا',
        error: e,
        stackTrace: stackTrace,
        context: {
          'folderPath': folderPath,
          'fileName': path.basename(imageFile.path),
          'fileSize': _formatFileSize(await imageFile.length()),
        },
      );
      return null;
    }
  }

  /// حذف صورة محلية
  Future<bool> deleteLocalImage(String imagePath) async {
    try {
      if (imagePath.isEmpty) return false;

      // التحقق من وجود الملف
      final file = File(imagePath);
      if (await file.exists()) {
        // حذف الملف
        await file.delete();
        AppLogger.info('تم حذف الصورة بنجاح: $imagePath');
        return true;
      } else {
        AppLogger.warning('الصورة غير موجودة: $imagePath');
        return false;
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حذف الصورة المحلية',
        error: e,
        stackTrace: stackTrace,
        context: {'imagePath': imagePath},
      );
      return false;
    }
  }

  /// Redimensionar imagen manteniendo la proporción
  Future<Uint8List?> resizeImage(
    File imageFile, {
    required int maxWidth,
    required int maxHeight,
    int quality = 85,
  }) async {
    try {
      return await FlutterImageCompress.compressWithFile(
        imageFile.path,
        minWidth: maxWidth,
        minHeight: maxHeight,
        quality: quality,
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al redimensionar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }
}
