import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/routes/app_routes.dart';
import '../models/user.dart';
import '../../../core/auth/models/user_role.dart';

import '../presenters/user_presenter.dart';
import 'user_groups_screen.dart';
import 'user_form_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../../core/services/image_service.dart';

import '../../../core/widgets/index.dart';
//import '../../../core/theme/index.dart';

/// شاشة إدارة المستخدمين
class UsersScreen extends StatefulWidget {
  const UsersScreen({Key? key}) : super(key: key);

  @override
  State<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends State<UsersScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchField = false;
  // final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند بدء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 🚀 تحميل بيانات المستخدمين باستخدام التحميل الكسول
      AppProviders.getUserPresenter().loadData();

      // 🚀 تحميل بيانات الأدوار باستخدام التحميل الكسول
      AppProviders.getPermissionPresenter().loadRoles();
    });

    // إضافة مستمع للبحث
    _searchController.addListener(() {
      AppProviders.getUserPresenter().setSearchQuery(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// عرض رسالة في شريط Snackbar
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // final theme = Theme.of(context);
    return Scaffold(
      appBar: AkAppBar(
        //backgroundColor: AppColors.primary,
        title: 'إدارة المستخدمين',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          // أيقونة إدارة الأدوار
          IconButton(
            icon: const Icon(Icons.admin_panel_settings),
            tooltip: 'إدارة الأدوار والصلاحيات',
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.rolesManagement);
            },
          ),
          // أيقونة تعيين الأدوار للمستخدمين
          IconButton(
            icon: const Icon(Icons.security),
            tooltip: 'تعيين الأدوار للمستخدمين',
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.userRoleAssignment);
            },
          ),
          // أيقونة إدارة المجموعات
          IconButton(
            icon: const Icon(Icons.group),
            tooltip: 'إدارة مجموعات المستخدمين',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UserGroupsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: _buildContent(),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () => _navigateToForm(context),
        tooltip: 'إضافة مستخدم',
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    return ListenableBuilder(
      listenable: AppProviders.getUserPresenter(),
      builder: (context, child) {
        final presenter = AppProviders.getUserPresenter();

        if (presenter.isLoading) {
          return const AkLoadingIndicator();
        }

        return CustomScrollView(
          slivers: [
            // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
            if (_showSearchField)
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: DynamicColors.surface(context),
                    boxShadow: [
                      BoxShadow(
                        color:
                            AppColors.primaryContainer.withValues(alpha: 0.05),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: AkSearchInput(
                    controller: _searchController,
                    hint:
                        'البحث في المستخدمين (الاسم، البريد، الهاتف، الدور)...',
                    onChanged: (value) {
                      presenter.setSearchQuery(value);
                    },
                    onClear: () {
                      _searchController.clear();
                      presenter.setSearchQuery('');
                    },
                  ),
                ),
              ),

            // خيارات الفلترة
            SliverToBoxAdapter(
              child: _buildFilterSection(),
            ),

            // قائمة المستخدمين
            _buildUsersSliverList(),
          ],
        );
      },
    );
  }

  /// بناء قسم الفلترة
  Widget _buildFilterSection() {
    final presenter = AppProviders.getUserPresenter();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: DynamicColors.surface(context),
        border: Border(
          bottom: BorderSide(
            color: DynamicColors.onSurface(context).withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: SwitchListTile(
              title: Text(
                'عرض المستخدمين غير النشطين',
                style: AppTypography(
                  fontSize: 14,
                  color: DynamicColors.onSurface(context),
                ),
              ),
              value: presenter.includeInactive,
              onChanged: (value) => presenter.toggleIncludeInactive(value),
              dense: true,
              contentPadding: EdgeInsets.zero,
              activeColor: DynamicColors.primary,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: DynamicColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: DynamicColors.primary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Text(
              'عدد المستخدمين: ${presenter.users.length}',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: DynamicColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين كـ Sliver
  Widget _buildUsersSliverList() {
    final presenter = AppProviders.getUserPresenter();

    if (presenter.errorMessage != null) {
      return SliverFillRemaining(
        child: Center(
          child: AkEmptyState(
            message: presenter.errorMessage!,
            icon: Icons.error_outline,
            onRefresh: () => presenter.loadData(),
          ),
        ),
      );
    }

    final users = presenter.users;
    if (users.isEmpty) {
      if (presenter.searchQuery.isNotEmpty) {
        return SliverFillRemaining(
          child: Center(
            child: AkEmptyState(
              message:
                  'لا يوجد مستخدمين مطابقين لبحثك "${presenter.searchQuery}"',
              icon: Icons.search_off,
              onRefresh: () {
                _searchController.clear();
                presenter.setSearchQuery('');
              },
            ),
          ),
        );
      }
      return SliverFillRemaining(
        child: Center(
          child: AkEmptyState(
            message: 'لا يوجد مستخدمين',
            icon: Icons.people_outline,
            onRefresh: () => _navigateToForm(context),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == users.length) {
            // مساحة إضافية في الأسفل للتمرير السلس
            return const SizedBox(height: 100);
          }

          final user = users[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: _buildUserCard(context, user, presenter),
          );
        },
        childCount: users.length + 1, // +1 للمساحة الإضافية
      ),
    );
  }

  /// بناء بطاقة المستخدم المحسنة
  Widget _buildUserCard(
      BuildContext context, User user, UserPresenter presenter) {
    final userGroup = user.userGroupId != null
        ? presenter.getGroupById(user.userGroupId ?? '')
        : null;

    final userBranch = user.branchId != null
        ? presenter.getBranchById(user.branchId ?? '')
        : null;

    // تحديد لون الحالة
    final statusColor = user.isActive ? AppColors.success : AppColors.error;

    // تحديد لون الدور
    final roleColor = _getRoleColor(user.roleId ?? "");

    // تحديد اسم الدور
    final roleName = user.roleName ?? _getRoleDisplayName(user.roleId ?? "");

    // تحديد أيقونة الدور
    final roleIcon = _getRoleIcon(user.roleId ?? "");

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 6),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: DynamicColors.surface(context),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: user.isActive
                      ? roleColor.withValues(alpha: 0.2)
                      : DynamicColors.onSurface(context).withValues(alpha: 0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: user.isActive
                        ? roleColor.withValues(alpha: 0.15)
                        : Colors.grey.withValues(alpha: 0.08),
                    spreadRadius: 0,
                    blurRadius: user.isActive ? 8 : 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الجزء العلوي: معلومات المستخدم الأساسية
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // صورة المستخدم مع تحسينات بصرية
                      Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  roleColor.withValues(alpha: 0.2),
                                  roleColor.withValues(alpha: 0.1),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: roleColor.withValues(alpha: 0.3),
                                  spreadRadius: 0,
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CircleAvatar(
                              radius: 26,
                              backgroundColor: Colors.transparent,
                              child: user.avatar != null &&
                                      user.avatar!.isNotEmpty
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(26),
                                      child: _buildUserAvatar({
                                        'id': user.id,
                                        'avatar': user.avatar,
                                      }),
                                    )
                                  : Icon(
                                      Icons.person_rounded,
                                      size: 28,
                                      color: roleColor,
                                    ),
                            ),
                          ),
                          // مؤشر الحالة
                          Positioned(
                            right: 0,
                            bottom: 0,
                            child: Container(
                              width: 14,
                              height: 14,
                              decoration: BoxDecoration(
                                color: statusColor,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: DynamicColors.surface(context),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: statusColor.withValues(alpha: 0.4),
                                    spreadRadius: 0,
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 16),
                      // معلومات المستخدم
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // اسم المستخدم
                            Text(
                              user.username,
                              style: AppTypography(
                                fontSize: 17,
                                fontWeight: FontWeight.bold,
                                color: user.isActive
                                    ? DynamicColors.onSurface(context)
                                    : DynamicColors.onSurface(context)
                                        .withValues(alpha: 0.6),
                              ),
                            ),
                            // الاسم الكامل
                            if (user.fullName.isNotEmpty &&
                                user.fullName != user.username)
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                  user.fullName,
                                  style: AppTypography(
                                    fontSize: 14,
                                    color: DynamicColors.onSurface(context)
                                        .withValues(alpha: 0.7),
                                  ),
                                ),
                              ),
                            // البريد الإلكتروني
                            if (user.email != null && user.email!.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 6),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.email_outlined,
                                      size: 14,
                                      color: DynamicColors.onSurface(context)
                                          .withValues(alpha: 0.6),
                                    ),
                                    const SizedBox(width: 6),
                                    Expanded(
                                      child: Text(
                                        user.email!,
                                        style: AppTypography(
                                          fontSize: 13,
                                          color:
                                              DynamicColors.onSurface(context)
                                                  .withValues(alpha: 0.7),
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            // رقم الهاتف
                            if (user.phone != null && user.phone!.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.phone_outlined,
                                      size: 14,
                                      color: DynamicColors.onSurface(context)
                                          .withValues(alpha: 0.6),
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      user.phone!,
                                      style: AppTypography(
                                        fontSize: 13,
                                        color: DynamicColors.onSurface(context)
                                            .withValues(alpha: 0.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                      // أزرار الإجراءات
                      Column(
                        children: [
                          // زر تغيير الحالة
                          Container(
                            decoration: BoxDecoration(
                              color: statusColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(
                                user.isActive
                                    ? Icons.toggle_on_rounded
                                    : Icons.toggle_off_rounded,
                                color: statusColor,
                                size: 24,
                              ),
                              onPressed: () {
                                presenter.toggleUserStatus(
                                    user.id, !user.isActive);
                              },
                              tooltip: user.isActive
                                  ? 'تعطيل المستخدم'
                                  : 'تفعيل المستخدم',
                            ),
                          ),
                          const SizedBox(height: 4),
                          // قائمة الخيارات
                          PopupMenuButton<String>(
                            icon: Icon(
                              Icons.more_vert_rounded,
                              color: DynamicColors.onSurface(context)
                                  .withValues(alpha: 0.7),
                            ),
                            onSelected: (value) =>
                                _handleMenuItemSelected(value, user, presenter),
                            itemBuilder: (context) => [
                              const PopupMenuItem<String>(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit_rounded),
                                    SizedBox(width: 8),
                                    Text('تعديل'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem<String>(
                                value: 'reset_password',
                                child: Row(
                                  children: [
                                    Icon(Icons.lock_reset_rounded),
                                    SizedBox(width: 8),
                                    Text('إعادة تعيين كلمة المرور'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem<String>(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(Icons.delete_rounded,
                                        color: AppColors.error),
                                    SizedBox(width: 8),
                                    Text('حذف',
                                        style: AppTypography(
                                            color: AppColors.error)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),

                  // الجزء السفلي: الدور والمجموعة (محسن لتجنب overflow)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // الشارات في Wrap لتجنب overflow
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 4.0,
                        children: [
                          // شارة الدور
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8.0, vertical: 4.0),
                            decoration: BoxDecoration(
                              color: roleColor.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(12.0),
                              border: Border.all(
                                color: roleColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  roleIcon,
                                  size: 14,
                                  color: roleColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  roleName,
                                  style: AppTypography(
                                    fontSize: 12,
                                    color: roleColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // شارة المجموعة
                          if (userGroup != null)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4.0),
                              decoration: BoxDecoration(
                                color: DynamicColors.primary
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12.0),
                                border: Border.all(
                                  color: DynamicColors.primary
                                      .withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.group_rounded,
                                    size: 14,
                                    color: DynamicColors.primary,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    userGroup.name,
                                    style: AppTypography(
                                      fontSize: 12,
                                      color: DynamicColors.primary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          // شارة الفرع
                          if (userBranch != null)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4.0),
                              decoration: BoxDecoration(
                                color: AppColors.info.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12.0),
                                border: Border.all(
                                  color: AppColors.info.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.business_rounded,
                                    size: 14,
                                    color: AppColors.info,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    userBranch.name,
                                    style: const AppTypography(
                                      fontSize: 12,
                                      color: AppColors.info,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),

                      // آخر تسجيل دخول في سطر منفصل
                      if (user.lastLogin != null) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 12,
                              color: DynamicColors.onSurface(context)
                                  .withValues(alpha: 0.6),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'آخر دخول: ${_formatDate(user.lastLogin!)}',
                              style: AppTypography(
                                fontSize: 11,
                                color: DynamicColors.onSurface(context)
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'الآن';
        }
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.year}/${date.month}/${date.day}';
    }
  }

  /// الحصول على أيقونة الدور
  IconData _getRoleIcon(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Icons.admin_panel_settings;
      case 'manager':
        return Icons.manage_accounts;
      case 'accountant':
        return Icons.account_balance;
      case 'cashier':
        return Icons.point_of_sale;
      case 'salesperson':
        return Icons.shopping_cart;
      case 'inventory_manager':
        return Icons.inventory;
      default:
        return Icons.person;
    }
  }

  /// التعامل مع اختيار عنصر من القائمة
  void _handleMenuItemSelected(
      String value, User user, UserPresenter presenter) {
    switch (value) {
      case 'edit':
        _navigateToForm(context, user: user);
        break;
      case 'reset_password':
        _showResetPasswordDialog(context, user, presenter);
        break;
      case 'delete':
        _showDeleteConfirmationDialog(context, user, presenter);
        break;
    }
  }

  /// عرض مربع حوار إعادة تعيين كلمة المرور
  void _showResetPasswordDialog(
      BuildContext context, User user, UserPresenter presenter) {
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين كلمة المرور'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('إعادة تعيين كلمة المرور للمستخدم: ${user.username}'),
              const SizedBox(height: 16),
              AkPasswordInput(
                controller: passwordController,
                label: 'كلمة المرور الجديدة',
                hint: 'أدخل كلمة المرور الجديدة',
                isRequired: true,
                showStrengthIndicator: true, // إضافة مؤشر قوة كلمة المرور
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AkPasswordInput(
                controller: confirmPasswordController,
                label: 'تأكيد كلمة المرور',
                hint: 'أعد إدخال كلمة المرور',
                isRequired: true,
                showStrengthIndicator: false, // لا نحتاج مؤشر في حقل التأكيد
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى تأكيد كلمة المرور';
                  }
                  if (value != passwordController.text) {
                    return 'كلمة المرور غير متطابقة';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context);
                // تنفيذ إعادة تعيين كلمة المرور
                final success = await presenter.updateUser(
                  user.copyWith(password: passwordController.text),
                  updatePassword: true,
                );

                // استخدام BuildContext في نطاق آمن
                if (!mounted) return;

                if (success) {
                  _showSnackBar('تم إعادة تعيين كلمة المرور بنجاح');
                } else {
                  _showSnackBar('فشل في إعادة تعيين كلمة المرور',
                      isError: true);
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(
      BuildContext context, User user, UserPresenter presenter) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستخدم: ${user.username}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.onPrimary,
            ),
            onPressed: () async {
              Navigator.pop(context);
              final success = await presenter.deleteUser(user.id);

              // استخدام BuildContext في نطاق آمن
              if (!mounted) return;

              if (success) {
                _showSnackBar('تم حذف المستخدم بنجاح');
              } else {
                _showSnackBar('فشل في حذف المستخدم', isError: true);
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الانتقال إلى شاشة نموذج المستخدم
  void _navigateToForm(BuildContext context, {User? user}) {
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    final presenter = AppProviders.getUserPresenter();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: user),
      ),
    ).then((_) {
      // إعادة تحميل البيانات عند العودة من شاشة النموذج
      if (mounted) {
        presenter.loadData();
      }
    });
  }

  /// الحصول على لون الدور
  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return AppColors.error;
      case 'manager':
        return AppColors.info;
      case 'accountant':
        return AppColors.success;
      case 'cashier':
        return AppColors.warning;
      default:
        return AppColors.secondary;
    }
  }

  /// الحصول على اسم العرض للدور
  String _getRoleDisplayName(String roleId) {
    if (roleId.isEmpty) {
      return 'غير محدد';
    }

    try {
      // استخدام متغير مخزن مسبقًا بدلاً من الحصول عليه أثناء البناء
      // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
      final permissionPresenter = AppProviders.getPermissionPresenter();

      // التحقق من وجود الأدوار قبل استخدامها
      if (permissionPresenter.roles.isNotEmpty) {
        final roles = permissionPresenter.roles;

        // البحث عن الدور بالمعرف
        final role = roles.firstWhere(
          (r) => r.id == roleId,
          orElse: () => UserRole(
            id: roleId,
            name: roleId,
            displayName: '',
            permissions: [],
          ),
        );

        // إذا وجدنا الدور، نعيد اسمه
        if (role.displayName.isNotEmpty) {
          return role.displayName;
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ، نستخدم الأسماء الافتراضية
    }

    // الأسماء الافتراضية للأدوار المعروفة
    switch (roleId.toLowerCase()) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير';
      case 'accountant':
        return 'محاسب';
      case 'cashier':
        return 'أمين صندوق';
      case 'user':
        return 'مستخدم';
      case 'guest':
        return 'زائر';
      default:
        return roleId;
    }
  }

  /// بناء صورة المستخدم من الملف المحلي أو الرابط أو قاعدة البيانات
  Widget _buildUserAvatar(Map<String, dynamic> user) {
    final String? avatarPath = user['avatar'];
    final String? userId = user['id'];

    if (avatarPath == null || avatarPath.isEmpty) {
      return _buildDefaultAvatar();
    }

    return FutureBuilder<dynamic>(
      future: _loadUserImage(userId, avatarPath),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              color: DynamicColors.surface(context),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(DynamicColors.primary),
                ),
              ),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return _buildDefaultAvatar();
        }

        final imageData = snapshot.data;
        return ClipOval(
          child: _buildImageFromData(imageData),
        );
      },
    );
  }

  /// تحميل صورة المستخدم
  Future<dynamic> _loadUserImage(String? userId, String avatarPath) async {
    if (userId == null) return null;

    return await ImageService().getImage(
      userId,
      'users',
      'avatar',
      avatarPath,
    );
  }

  /// بناء الصورة من البيانات
  Widget _buildImageFromData(dynamic imageData) {
    if (imageData is File) {
      return Image.file(
        imageData,
        fit: BoxFit.cover,
        width: 52,
        height: 52,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else if (imageData is Uint8List) {
      return Image.memory(
        imageData,
        fit: BoxFit.cover,
        width: 52,
        height: 52,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else if (imageData is String) {
      return Image.network(
        imageData,
        fit: BoxFit.cover,
        width: 52,
        height: 52,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else {
      return _buildDefaultAvatar();
    }
  }

  /// بناء صورة المستخدم الافتراضية
  Widget _buildDefaultAvatar() {
    return Container(
      width: 52,
      height: 52,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person_rounded,
        color: AppColors.primary,
        size: 28,
      ),
    );
  }
}
