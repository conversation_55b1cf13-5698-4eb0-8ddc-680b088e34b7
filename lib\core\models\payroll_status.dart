/// حالات الرواتب
enum PayrollStatus {
  /// مسودة
  draft,

  /// معتمد
  approved,

  /// مدفوع
  paid,

  /// ملغي
  cancelled,
}

/// الحصول على اسم حالة الراتب
String getPayrollStatusName(PayrollStatus status) {
  switch (status) {
    case PayrollStatus.draft:
      return 'مسودة';
    case PayrollStatus.approved:
      return 'معتمد';
    case PayrollStatus.paid:
      return 'مدفوع';
    case PayrollStatus.cancelled:
      return 'ملغي';
  }
}

/// الحصول على حالة الراتب من الاسم
PayrollStatus getPayrollStatusFromName(String name) {
  switch (name) {
    case 'مسودة':
      return PayrollStatus.draft;
    case 'معتمد':
      return PayrollStatus.approved;
    case 'مدفوع':
      return PayrollStatus.paid;
    case 'ملغي':
      return PayrollStatus.cancelled;
    default:
      return PayrollStatus.draft;
  }
}

/// الحصول على حالة الراتب من القيمة النصية
PayrollStatus getPayrollStatusFromString(String value) {
  switch (value) {
    case 'draft':
      return PayrollStatus.draft;
    case 'approved':
      return PayrollStatus.approved;
    case 'paid':
      return PayrollStatus.paid;
    case 'cancelled':
      return PayrollStatus.cancelled;
    default:
      return PayrollStatus.draft;
  }
}
