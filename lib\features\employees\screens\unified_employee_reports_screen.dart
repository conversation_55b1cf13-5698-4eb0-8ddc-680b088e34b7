import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tajer_plus/core/widgets/data_table_widget.dart';

import 'package:tajer_plus/core/models/employee.dart';
import 'package:tajer_plus/core/models/employee_status.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

/// شاشة موحدة لتقارير الموظفين
/// تم تصميمها لتكون أكثر كفاءة وسرعة في الاستخدام
class UnifiedEmployeeReportsScreen extends StatefulWidget {
  const UnifiedEmployeeReportsScreen({Key? key}) : super(key: key);

  @override
  State<UnifiedEmployeeReportsScreen> createState() =>
      _UnifiedEmployeeReportsScreenState();
}

class _UnifiedEmployeeReportsScreenState
    extends State<UnifiedEmployeeReportsScreen>
    with SingleTickerProviderStateMixin {
  // حالة الشاشة
  bool _isLoading = false;
  bool _isFilterExpanded = false;
  bool _isReportGenerated = false;
  String _selectedReportType = 'تقرير الموظفين';
  String _selectedDepartment = 'الكل';
  String _selectedStatus = 'الكل';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String? _selectedEmployeeId;

  // وحدات التحكم للبحث والتصفية
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // قوائم البيانات
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  final List<String> _departments = [
    'الكل',
    'المبيعات',
    'المالية',
    'الموارد البشرية',
    'المستودعات',
    'تكنولوجيا المعلومات'
  ];
  final List<String> _statusOptions = [
    'الكل',
    'نشط',
    'غير نشط',
    'في إجازة',
    'منتهي الخدمة'
  ];
  final List<String> _reportTypes = [
    'تقرير الموظفين',
    'تقرير الرواتب',
    'تقرير الإجازات',
    'تقرير الحضور والانصراف',
    'تقرير تكاليف الموظفين',
    'تقرير أداء الموظفين',
  ];

  // متحكم التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _reportTypes.length, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadEmployees();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  /// معالجة تغيير التبويب
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _selectedReportType = _reportTypes[_tabController.index];
        _isReportGenerated = false;
      });
    }
  }

  /// تحميل بيانات الموظفين
  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة جلب بيانات الموظفين
      // مثال:
      // final employeeService = EmployeeService();
      // final employees = await employeeService.getEmployees();

      // بيانات تجريبية للعرض
      await Future.delayed(const Duration(seconds: 1));
      final dummyEmployees = [
        Employee(
          id: '1',
          employeeId: 'EMP001',
          fullName: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '0123456789',
          position: 'مدير مبيعات',
          department: 'المبيعات',
          basicSalary: 5000,
          hireDate: DateTime.now().subtract(const Duration(days: 365)),
          status: employeeStatusToString(EmployeeStatus.active),
        ),
        Employee(
          id: '2',
          employeeId: 'EMP002',
          fullName: 'سارة أحمد',
          email: '<EMAIL>',
          phone: '0123456788',
          position: 'محاسب',
          department: 'المالية',
          basicSalary: 4500,
          hireDate: DateTime.now().subtract(const Duration(days: 180)),
          status: employeeStatusToString(EmployeeStatus.active),
        ),
        Employee(
          id: '3',
          employeeId: 'EMP003',
          fullName: 'محمد علي',
          email: '<EMAIL>',
          phone: '0123456787',
          position: 'مندوب مبيعات',
          department: 'المبيعات',
          basicSalary: 3500,
          hireDate: DateTime.now().subtract(const Duration(days: 90)),
          status: employeeStatusToString(EmployeeStatus.onLeave),
        ),
      ];

      setState(() {
        _employees = dummyEmployees;
        _filteredEmployees = dummyEmployees;
        _isLoading = false;
        if (_selectedEmployeeId == null && _employees.isNotEmpty) {
          _selectedEmployeeId = _employees[0].id;
        }
      });
    } catch (e) {
      // معالجة الأخطاء
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل بيانات الموظفين');
    }
  }

  /// توليد التقرير
  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة توليد التقرير
      // مثال:
      // final reportService = ReportService();
      // await reportService.generateReport(
      //   reportType: _selectedReportType,
      //   startDate: _startDate,
      //   endDate: _endDate,
      //   departmentId: _selectedDepartment == 'الكل' ? null : _selectedDepartment,
      //   employeeId: _selectedEmployeeId,
      // );

      // محاكاة عملية توليد التقرير
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isLoading = false;
        _isReportGenerated = true;
      });

      _showSuccessSnackBar('تم توليد التقرير بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء توليد التقرير');
    }
  }

  /// طباعة التقرير
  Future<void> _printReport() async {
    if (!_isReportGenerated) {
      _showErrorSnackBar('يرجى توليد التقرير أولاً قبل الطباعة');
      return;
    }

    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري إعداد التقرير للطباعة...');

    // هنا سيتم استدعاء خدمة الطباعة
    // مثال:
    // final printService = PrintService();
    // await printService.printReport(
    //   reportType: _selectedReportType,
    //   startDate: _startDate,
    //   endDate: _endDate,
    //   departmentId: _selectedDepartment == 'الكل' ? null : _selectedDepartment,
    //   employeeId: _selectedEmployeeId,
    // );

    // محاكاة عملية الطباعة
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم إرسال التقرير للطباعة بنجاح');
  }

  /// تصدير التقرير
  Future<void> _exportReport() async {
    if (!_isReportGenerated) {
      _showErrorSnackBar('يرجى توليد التقرير أولاً قبل التصدير');
      return;
    }

    // عرض مربع حوار لاختيار نوع التصدير
    final exportType = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: const Text('اختر صيغة التصدير:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('pdf'),
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('excel'),
            child: const Text('Excel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('csv'),
            child: const Text('CSV'),
          ),
        ],
      ),
    );

    if (exportType == null) return;

    // عرض رسالة للمستخدم
    _showInfoSnackBar('جاري تصدير التقرير بصيغة $exportType...');

    // هنا سيتم استدعاء خدمة التصدير
    // مثال:
    // final exportService = ExportService();
    // await exportService.exportReport(
    //   reportType: _selectedReportType,
    //   exportType: exportType,
    //   startDate: _startDate,
    //   endDate: _endDate,
    //   departmentId: _selectedDepartment == 'الكل' ? null : _selectedDepartment,
    //   employeeId: _selectedEmployeeId,
    // );

    // محاكاة عملية التصدير
    await Future.delayed(const Duration(seconds: 1));

    _showSuccessSnackBar('تم تصدير التقرير بنجاح');
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  /// عرض رسالة معلومات
  void _showInfoSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الموظفين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadEmployees,
          ),
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة التقرير',
            onPressed: _printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير التقرير',
            onPressed: _exportReport,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _reportTypes.map((type) => Tab(text: type)).toList(),
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildFilterSection(),
                const SizedBox(height: AppDimensions.spacing16),
                _buildActionButtons(),
                const SizedBox(height: AppDimensions.spacing16),
                Expanded(
                  child: _isReportGenerated
                      ? _buildReportContent()
                      : const Center(
                          child: Text(
                              'قم بتحديد معايير التقرير ثم اضغط على "توليد التقرير"')),
                ),
              ],
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء قسم التصفية
  Widget _buildFilterSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'معايير التقرير',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _isFilterExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.lightTextSecondary,
                  ),
                  onPressed: () {
                    setState(() {
                      _isFilterExpanded = !_isFilterExpanded;
                    });
                  },
                ),
              ],
            ),
            if (_isFilterExpanded) ...[
              const SizedBox(height: AppDimensions.spacing16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'من تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      controller: TextEditingController(
                        text: DateFormat('yyyy/MM/dd').format(_startDate),
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _startDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            _startDate = date;
                            _isReportGenerated = false;
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'إلى تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      controller: TextEditingController(
                        text: DateFormat('yyyy/MM/dd').format(_endDate),
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _endDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            _endDate = date;
                            _isReportGenerated = false;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing16),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'القسم',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedDepartment,
                      items: _departments.map((department) {
                        return DropdownMenuItem<String>(
                          value: department,
                          child: Text(department),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedDepartment = value;
                            _isReportGenerated = false;
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'الحالة',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedStatus,
                      items: _statusOptions.map((status) {
                        return DropdownMenuItem<String>(
                          value: status,
                          child: Text(status),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedStatus = value;
                            _isReportGenerated = false;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الموظف',
                  border: OutlineInputBorder(),
                ),
                value: _selectedEmployeeId,
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('جميع الموظفين'),
                  ),
                  ..._employees.map((employee) {
                    return DropdownMenuItem<String>(
                      value: employee.id,
                      child: Text(employee.fullName),
                    );
                  }).toList(),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedEmployeeId = value;
                    _isReportGenerated = false;
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.bar_chart),
          label: const Text('توليد التقرير'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          onPressed: _generateReport,
        ),
        const SizedBox(width: 16),
        OutlinedButton.icon(
          icon: const Icon(Icons.refresh),
          label: const Text('إعادة تعيين'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          onPressed: () {
            setState(() {
              _selectedDepartment = 'الكل';
              _selectedStatus = 'الكل';
              _startDate = DateTime.now().subtract(const Duration(days: 30));
              _endDate = DateTime.now();
              _selectedEmployeeId = null;
              _isReportGenerated = false;
            });
          },
        ),
      ],
    );
  }

  /// بناء محتوى التقرير
  Widget _buildReportContent() {
    // هنا سيتم عرض محتوى التقرير حسب النوع المحدد
    switch (_selectedReportType) {
      case 'تقرير الموظفين':
        return _buildEmployeesReport();
      case 'تقرير الرواتب':
        return _buildSalaryReport();
      case 'تقرير الإجازات':
        return const Center(child: Text('تقرير الإجازات غير متوفر حالياً'));
      case 'تقرير الحضور والانصراف':
        return const Center(
            child: Text('تقرير الحضور والانصراف غير متوفر حالياً'));
      case 'تقرير تكاليف الموظفين':
        return const Center(
            child: Text('تقرير تكاليف الموظفين غير متوفر حالياً'));
      case 'تقرير أداء الموظفين':
        return const Center(
            child: Text('تقرير أداء الموظفين غير متوفر حالياً'));
      default:
        return const Center(child: Text('التقرير غير متوفر'));
    }
  }

  /// بناء تقرير الموظفين
  Widget _buildEmployeesReport() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'تقرير الموظفين: ${DateFormat('yyyy/MM/dd').format(_startDate)} - ${DateFormat('yyyy/MM/dd').format(_endDate)}',
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            const Divider(),
            Expanded(
              child: _filteredEmployees.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : DataTableWidget<Employee>(
                      columns: const [
                        DataColumn(label: Text('رقم الموظف')),
                        DataColumn(label: Text('الاسم')),
                        DataColumn(label: Text('القسم')),
                        DataColumn(label: Text('المنصب')),
                        DataColumn(label: Text('الراتب')),
                        DataColumn(label: Text('تاريخ التعيين')),
                        DataColumn(label: Text('الحالة')),
                      ],
                      items: _filteredEmployees,
                      rowBuilder: (employee) {
                        return DataRow(
                          cells: [
                            DataCell(Text(employee.employeeId)),
                            DataCell(Text(employee.fullName)),
                            DataCell(Text(employee.department ?? '')),
                            DataCell(Text(employee.position ?? '')),
                            DataCell(
                                Text((employee.basicSalary ?? 0).toString())),
                            DataCell(Text(employee.hireDate != null
                                ? DateFormat('yyyy/MM/dd')
                                    .format(employee.hireDate!)
                                : '')),
                            DataCell(Text(getEmployeeStatusName(
                                getEmployeeStatusFromString(employee.status)))),
                          ],
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تقرير الرواتب
  Widget _buildSalaryReport() {
    // بيانات تجريبية للعرض
    final salaryData = _filteredEmployees.map((employee) {
      final salary = employee.basicSalary ?? 0.0;
      return {
        'employee': employee,
        'basicSalary': salary,
        'allowances': salary * 0.1,
        'deductions': salary * 0.05,
        'netSalary': salary * 1.05,
      };
    }).toList();

    double totalBasicSalary = 0;
    double totalAllowances = 0;
    double totalDeductions = 0;
    double totalNetSalary = 0;

    for (var item in salaryData) {
      totalBasicSalary += item['basicSalary'] as double;
      totalAllowances += item['allowances'] as double;
      totalDeductions += item['deductions'] as double;
      totalNetSalary += item['netSalary'] as double;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'تقرير الرواتب: ${DateFormat('yyyy/MM/dd').format(_startDate)} - ${DateFormat('yyyy/MM/dd').format(_endDate)}',
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            const Divider(),
            Expanded(
              child: salaryData.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : SingleChildScrollView(
                      child: Column(
                        children: [
                          DataTable(
                            columns: const [
                              DataColumn(label: Text('الموظف')),
                              DataColumn(label: Text('الراتب الأساسي')),
                              DataColumn(label: Text('البدلات')),
                              DataColumn(label: Text('الاستقطاعات')),
                              DataColumn(label: Text('صافي الراتب')),
                            ],
                            rows: salaryData.map((item) {
                              final employee = item['employee'] as Employee;
                              return DataRow(
                                cells: [
                                  DataCell(Text(employee.fullName)),
                                  DataCell(
                                      Text(item['basicSalary'].toString())),
                                  DataCell(Text(item['allowances'].toString())),
                                  DataCell(Text(item['deductions'].toString())),
                                  DataCell(Text(item['netSalary'].toString())),
                                ],
                              );
                            }).toList(),
                          ),
                          const Divider(),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('الإجمالي',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold)),
                                const Spacer(),
                                Text(totalBasicSalary.toStringAsFixed(2),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold)),
                                const Spacer(),
                                Text(totalAllowances.toStringAsFixed(2),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold)),
                                const Spacer(),
                                Text(totalDeductions.toStringAsFixed(2),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold)),
                                const Spacer(),
                                Text(totalNetSalary.toStringAsFixed(2),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
